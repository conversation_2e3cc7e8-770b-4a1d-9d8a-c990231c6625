#!/usr/bin/env python3
"""
Test script for OpenAlgo Institutional Strategy webhook
"""

import requests
import json

def test_institutional_webhook():
    """Test the institutional strategy webhook"""
    
    # Test data - simulating a TradingView alert
    test_signal = {
        "symbol": "RELIANCE",
        "signal": "BUY",
        "price": "2500.00",
        "confidence": 85,
        "timeframe": "30m",
        "strategy": "institutional_breakout",
        "volume": "1000000",
        "timestamp": "2025-06-24T16:35:00",
        "block_trade_detected": True,
        "mtf_confirmed": True,
        "cvd_confirmation": True
    }
    
    # Webhook URL
    url = "http://127.0.0.1:5000/webhook/institutional"
    
    try:
        print("🚀 Testing Institutional Strategy Webhook...")
        print(f"📡 URL: {url}")
        print(f"📊 Test Signal: {json.dumps(test_signal, indent=2)}")
        print("-" * 50)
        
        # Send POST request
        response = requests.post(
            url,
            json=test_signal,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📈 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        print(f"💬 Response Body:")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
            print(json.dumps(response_data, indent=2))
        else:
            print(response.text)
        
        if response.status_code == 200:
            print("\n✅ Webhook test SUCCESSFUL!")
            return True
        else:
            print(f"\n❌ Webhook test FAILED with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"\n❌ Request failed: {str(e)}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        return False

def test_webhook_endpoints():
    """Test all webhook endpoints"""
    
    print("🧪 Testing OpenAlgo Institutional Strategy Webhooks")
    print("=" * 60)
    
    # Test 1: Test endpoint
    print("\n1️⃣ Testing /webhook/test endpoint...")
    try:
        response = requests.get("http://127.0.0.1:5000/webhook/test", timeout=10)
        if response.status_code == 200:
            print("✅ Test endpoint working!")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Test endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Test endpoint error: {str(e)}")
    
    # Test 2: Institutional webhook
    print("\n2️⃣ Testing /webhook/institutional endpoint...")
    success = test_institutional_webhook()
    
    # Test 3: Invalid data test
    print("\n3️⃣ Testing with invalid data...")
    try:
        invalid_signal = {"invalid": "data"}
        response = requests.post(
            "http://127.0.0.1:5000/webhook/institutional",
            json=invalid_signal,
            timeout=10
        )
        print(f"Invalid data response: {response.status_code}")
        if response.headers.get('content-type', '').startswith('application/json'):
            print(f"Response: {response.json()}")
    except Exception as e:
        print(f"❌ Invalid data test error: {str(e)}")
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All critical tests PASSED! Your webhook is ready for TradingView!")
    else:
        print("⚠️ Some tests failed. Please check the configuration.")

if __name__ == "__main__":
    test_webhook_endpoints()
