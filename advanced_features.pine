//@version=6
// ═══════════════════════════════════════════════════════════════════════════════════════
// ADVANCED INSTITUTIONAL TRADING FEATURES
// ═══════════════════════════════════════════════════════════════════════════════════════
// This file contains advanced features that can be added to the main strategy
// for enhanced institutional detection and market analysis

// ADVANCED VOLUME ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Volume Profile Analysis
f_volume_profile_poc(length) =>
    var float poc_price = na
    var float max_volume = 0.0
    
    for i = 0 to length - 1
        price_level = close[i]
        volume_at_level = volume[i]
        
        if volume_at_level > max_volume
            max_volume := volume_at_level
            poc_price := price_level
    
    poc_price

// Institutional Volume Signature
f_institutional_volume_signature() =>
    // Large volume with minimal price movement indicates institutional activity
    volume_percentile = ta.percentrank(volume, 50)
    price_change = math.abs(ta.change(close)) / close * 100
    
    // Institutional signature: High volume (>80th percentile) with low price change (<0.5%)
    institutional_signature = volume_percentile > 80 and price_change < 0.5
    institutional_signature

// Dark Pool Activity Detection
f_dark_pool_activity() =>
    // Detect potential dark pool activity through volume-price analysis
    typical_volume = ta.sma(volume, 20)
    volume_ratio = volume / typical_volume
    
    // Large volume with price staying within tight range
    price_range = (high - low) / close * 100
    dark_pool_signal = volume_ratio > 2.0 and price_range < 1.0
    dark_pool_signal

// SMART MONEY INDICATORS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Wyckoff Accumulation/Distribution Detection
f_wyckoff_phase() =>
    // Phase A: Stopping action after downtrend
    // Phase B: Building cause
    // Phase C: Spring or test
    // Phase D: Signs of strength
    
    volume_ma = ta.sma(volume, 20)
    price_ma = ta.sma(close, 20)
    
    // Simplified Wyckoff phase detection
    phase_a = volume > volume_ma * 1.5 and math.abs(ta.change(close)) < close * 0.01
    phase_b = volume < volume_ma and close > price_ma
    phase_c = close < ta.lowest(low, 10) and volume > volume_ma
    phase_d = close > price_ma and volume > volume_ma and ta.change(close) > 0
    
    [phase_a, phase_b, phase_c, phase_d]

// Composite Index (Similar to Composite Man concept)
f_composite_index() =>
    // Combines multiple institutional indicators
    obv_trend = ta.change(ta.obv, 5) > 0 ? 1 : -1
    ad_trend = ta.change(ta.ad, 5) > 0 ? 1 : -1
    mfi_signal = ta.mfi(hlc3, 14) > 50 ? 1 : -1
    volume_trend = volume > ta.sma(volume, 20) ? 1 : -1
    
    composite_score = (obv_trend + ad_trend + mfi_signal + volume_trend) / 4
    composite_score

// MARKET STRUCTURE ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Order Flow Imbalance Detection
f_order_flow_imbalance() =>
    // Detect imbalances in buy/sell pressure
    buying_pressure = close > open ? volume : 0
    selling_pressure = close < open ? volume : 0
    
    buy_volume = ta.sma(buying_pressure, 14)
    sell_volume = ta.sma(selling_pressure, 14)
    
    imbalance_ratio = buy_volume / (sell_volume + 1) // Add 1 to avoid division by zero
    
    // Strong buying when ratio > 2, strong selling when ratio < 0.5
    [imbalance_ratio > 2, imbalance_ratio < 0.5]

// Institutional Footprint Scanner
f_institutional_footprint() =>
    // Scan for institutional trading patterns
    
    // 1. Large block trades (volume spikes)
    volume_spike = volume > ta.sma(volume, 20) * 2.5
    
    // 2. Iceberg orders (consistent volume at price levels)
    volume_consistency = ta.stdev(volume, 10) < ta.sma(volume, 10) * 0.3
    
    // 3. Time-based patterns (institutional trading times)
    hour_of_day = hour(time)
    institutional_hours = hour_of_day >= 10 and hour_of_day <= 14 // 10 AM to 2 PM IST
    
    // 4. Price level defense (support at round numbers)
    round_number = close % 10 == 0 or close % 5 == 0
    price_defense = round_number and volume > ta.sma(volume, 20)
    
    footprint_score = (volume_spike ? 1 : 0) + (volume_consistency ? 1 : 0) + 
                     (institutional_hours ? 1 : 0) + (price_defense ? 1 : 0)
    
    footprint_score >= 2

// ADVANCED ENTRY/EXIT LOGIC
// ═══════════════════════════════════════════════════════════════════════════════════════

// Multi-Timeframe Confirmation
f_mtf_confirmation() =>
    // Get higher timeframe data for confirmation
    htf_close = request.security(syminfo.tickerid, "1D", close)
    htf_volume = request.security(syminfo.tickerid, "1D", volume)
    htf_obv = request.security(syminfo.tickerid, "1D", ta.obv)
    
    // Higher timeframe trend confirmation
    htf_trend_up = htf_close > ta.sma(htf_close, 20)
    htf_volume_confirm = htf_volume > ta.sma(htf_volume, 20)
    htf_obv_confirm = htf_obv > ta.sma(htf_obv, 14)
    
    [htf_trend_up and htf_volume_confirm and htf_obv_confirm, 
     not htf_trend_up and htf_volume_confirm and not htf_obv_confirm]

// Dynamic Position Sizing
f_dynamic_position_size(volatility, volume_strength) =>
    // Adjust position size based on market conditions
    base_size = 10.0 // Base position size percentage
    
    // Reduce size in high volatility
    volatility_factor = volatility > 2.0 ? 0.5 : volatility > 1.5 ? 0.75 : 1.0
    
    // Increase size with strong volume confirmation
    volume_factor = volume_strength > 2.0 ? 1.5 : volume_strength > 1.5 ? 1.25 : 1.0
    
    adjusted_size = base_size * volatility_factor * volume_factor
    math.min(adjusted_size, 20.0) // Cap at 20%

// RISK MANAGEMENT ENHANCEMENTS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Adaptive Stop Loss
f_adaptive_stop_loss(entry_price, volatility, is_long) =>
    // Adjust stop loss based on volatility
    base_stop_pct = 2.0
    volatility_multiplier = math.max(0.5, math.min(3.0, volatility))
    
    adaptive_stop_pct = base_stop_pct * volatility_multiplier
    
    if is_long
        entry_price * (1 - adaptive_stop_pct / 100)
    else
        entry_price * (1 + adaptive_stop_pct / 100)

// Trailing Stop with Volume Confirmation
f_volume_trailing_stop(entry_price, current_price, volume_strength, is_long) =>
    // Trail stop only when volume confirms the move
    base_trail_pct = 1.0
    
    if volume_strength > 1.5 // Strong volume confirmation
        trail_pct = base_trail_pct
    else
        trail_pct = base_trail_pct * 1.5 // Wider trail without volume confirmation
    
    if is_long
        current_price * (1 - trail_pct / 100)
    else
        current_price * (1 + trail_pct / 100)

// MARKET REGIME DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Market Regime Classification
f_market_regime() =>
    // Classify market as trending, ranging, or volatile
    price_range = ta.atr(20)
    volume_avg = ta.sma(volume, 20)
    
    trend_strength = math.abs(ta.change(ta.sma(close, 20), 20)) / price_range
    volume_consistency = ta.stdev(volume, 20) / volume_avg
    
    // Regime classification
    trending = trend_strength > 1.5 and volume_consistency < 0.5
    ranging = trend_strength < 0.5 and volume_consistency < 0.3
    volatile = volume_consistency > 0.7
    
    regime = trending ? "TRENDING" : ranging ? "RANGING" : volatile ? "VOLATILE" : "MIXED"
    regime

// SECTOR ROTATION ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Relative Strength vs NIFTY
f_relative_strength_nifty() =>
    // Compare stock performance to NIFTY 50
    nifty_close = request.security("NSE:NIFTY", timeframe.period, close)
    
    stock_return = ta.roc(close, 20)
    nifty_return = ta.roc(nifty_close, 20)
    
    relative_strength = stock_return - nifty_return
    rs_ma = ta.sma(relative_strength, 10)
    
    // Strong relative strength indicates institutional interest
    rs_ma > 2.0

// EXAMPLE USAGE IN STRATEGY
// ═══════════════════════════════════════════════════════════════════════════════════════

// These functions can be integrated into the main strategy like this:

// // Get advanced signals
// institutional_signature = f_institutional_volume_signature()
// dark_pool_activity = f_dark_pool_activity()
// [wyckoff_a, wyckoff_b, wyckoff_c, wyckoff_d] = f_wyckoff_phase()
// composite_score = f_composite_index()
// institutional_footprint = f_institutional_footprint()
// [mtf_bullish, mtf_bearish] = f_mtf_confirmation()
// market_regime = f_market_regime()
// relative_strength = f_relative_strength_nifty()

// // Enhanced entry conditions
// enhanced_long_entry = long_entry and institutional_footprint and 
//                      composite_score > 0.5 and mtf_bullish and 
//                      market_regime != "VOLATILE"

// // Dynamic position sizing
// current_volatility = ta.atr(14) / close * 100
// volume_strength = volume / ta.sma(volume, 20)
// position_size = f_dynamic_position_size(current_volatility, volume_strength)

// NOTES FOR IMPLEMENTATION:
// 1. Add these functions to the main strategy file
// 2. Integrate the signals into your entry/exit logic
// 3. Test thoroughly before live trading
// 4. Adjust parameters based on backtesting results
// 5. Consider computational limits of Pine Script
