#!/usr/bin/env python3
"""
Startup script for OpenAlgo Institutional Strategy
Ensures proper initialization and health checks
"""

import os
import sys
import time
import logging
from institutional_app import app, logger

def check_environment():
    """Check if all required environment variables are set"""
    required_vars = [
        'SECRET_KEY',
        'WEBHOOK_SECRET'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.warning(f"⚠️ Missing environment variables: {', '.join(missing_vars)}")
        logger.info("💡 Using default values for development")
    else:
        logger.info("✅ All required environment variables are set")

def health_check():
    """Perform basic health checks"""
    try:
        # Check database initialization
        from strategies.institutional_strategy import institutional_strategy
        logger.info("✅ Strategy module loaded successfully")
        
        # Check Flask app
        with app.test_client() as client:
            response = client.get('/webhook/test')
            if response.status_code == 200:
                logger.info("✅ Webhook endpoint is healthy")
            else:
                logger.error(f"❌ Webhook endpoint failed: {response.status_code}")
                
    except Exception as e:
        logger.error(f"❌ Health check failed: {str(e)}")
        return False
    
    return True

def main():
    """Main startup function"""
    logger.info("🚀 Starting OpenAlgo Institutional Strategy...")
    logger.info("📊 Strategy: Institutional Breakout (184.42 Profit Factor)")
    
    # Environment check
    check_environment()
    
    # Health check
    if not health_check():
        logger.error("❌ Health check failed. Exiting...")
        sys.exit(1)
    
    # Get configuration
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    # Railway deployment info
    railway_env = os.getenv('RAILWAY_ENVIRONMENT_NAME', 'local')
    if railway_env != 'local':
        logger.info(f"🚂 Running on Railway environment: {railway_env}")
        logger.info(f"🌐 Public URL will be available after deployment")
    
    logger.info(f"🌐 Server starting on {host}:{port}")
    logger.info(f"📡 Webhook endpoint: /webhook/institutional")
    logger.info(f"🧪 Test endpoint: /webhook/test")
    logger.info(f"📊 Dashboard: /dashboard")
    
    # Start the application
    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        logger.info("👋 Shutting down gracefully...")
    except Exception as e:
        logger.error(f"❌ Application error: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
