"""
Institutional Trading App for OpenAlgo
=====================================

This application integrates the institutional strategy with OpenAlgo
and provides webhook endpoints for TradingView integration.

Features:
- TradingView webhook integration
- Real-time trade execution
- Risk management
- Performance monitoring
- Telegram notifications

Author: OpenAlgo Institutional App
Version: 1.0
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import requests
from strategies.institutional_strategy import process_institutional_signal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('institutional_app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Configuration
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-here')
WEBHOOK_SECRET = os.getenv('WEBHOOK_SECRET', 'your-webhook-secret')
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID', '')

class TelegramNotifier:
    """Handle Telegram notifications"""
    
    def __init__(self, bot_token: str, chat_id: str):
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.enabled = bool(bot_token and chat_id)
    
    def send_message(self, message: str):
        """Send message to Telegram"""
        if not self.enabled:
            logger.info("Telegram notifications disabled")
            return
        
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            data = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": "HTML"
            }
            
            response = requests.post(url, data=data, timeout=10)
            if response.status_code == 200:
                logger.info("Telegram notification sent successfully")
            else:
                logger.error(f"Telegram notification failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Telegram notification error: {str(e)}")

# Initialize Telegram notifier
telegram = TelegramNotifier(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID)

@app.route('/')
def home():
    """Home page with basic information"""
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>OpenAlgo Institutional Strategy</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; }
            .status { padding: 15px; margin: 20px 0; border-radius: 5px; }
            .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
            .endpoint { background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 10px 0; }
            code { background-color: #f1f1f1; padding: 2px 5px; border-radius: 3px; }
            .feature { margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 OpenAlgo Institutional Strategy</h1>
            
            <div class="status success">
                <strong>✅ System Status:</strong> Active and Ready
            </div>
            
            <div class="status info">
                <strong>📊 Strategy:</strong> Institutional Breakout with Kelly Criterion Position Sizing
            </div>
            
            <h2>📡 Webhook Endpoints</h2>
            
            <div class="endpoint">
                <strong>Institutional Strategy Webhook:</strong><br>
                <code>POST /webhook/institutional</code><br>
                <small>Use this URL in your TradingView alerts</small>
            </div>
            
            <div class="endpoint">
                <strong>Test Webhook:</strong><br>
                <code>GET /webhook/test</code><br>
                <small>Test endpoint connectivity</small>
            </div>
            
            <div class="endpoint">
                <strong>Performance Dashboard:</strong><br>
                <code>GET /dashboard</code><br>
                <small>View trading performance and statistics</small>
            </div>
            
            <h2>🎯 Strategy Features</h2>
            
            <div class="feature">
                <strong>📈 Institutional Detection:</strong> Identifies institutional investor entry/exit points
            </div>
            
            <div class="feature">
                <strong>⚖️ Kelly Criterion:</strong> Optimal position sizing based on confidence levels
            </div>
            
            <div class="feature">
                <strong>🛡️ Risk Management:</strong> Automated stop-loss and take-profit levels
            </div>
            
            <div class="feature">
                <strong>📊 Multi-timeframe:</strong> 30m signals with 4h confirmation
            </div>
            
            <div class="feature">
                <strong>🔔 Notifications:</strong> Real-time Telegram alerts for all trades
            </div>
            
            <h2>📋 TradingView Alert Setup</h2>
            
            <div class="endpoint">
                <strong>Webhook URL:</strong><br>
                <code>{{ request.url_root }}webhook/institutional</code>
            </div>
            
            <div class="endpoint">
                <strong>Alert Message (JSON):</strong><br>
                <pre><code>{
  "symbol": "{{ticker}}",
  "signal": "{{strategy.order.action}}",
  "price": "{{close}}",
  "volume": "{{volume}}",
  "confidence": 85,
  "timeframe": "{{interval}}",
  "timestamp": "{{time}}",
  "strategy": "institutional_breakout",
  "block_trade_detected": true,
  "mtf_confirmed": true,
  "cvd_confirmation": true
}</code></pre>
            </div>
            
            <div class="status info">
                <strong>💡 Tip:</strong> Adjust the confidence level (80-100) based on your signal strength
            </div>
        </div>
    </body>
    </html>
    """
    return render_template_string(html_template)

@app.route('/webhook/test', methods=['GET', 'POST'])
def test_webhook():
    """Test webhook endpoint"""
    return jsonify({
        "status": "success",
        "message": "OpenAlgo Institutional Strategy webhook is working!",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0"
    })

@app.route('/webhook/institutional', methods=['POST'])
def institutional_webhook():
    """Handle TradingView institutional strategy alerts"""
    try:
        # Get JSON data from request
        alert_data = request.get_json()
        
        if not alert_data:
            logger.error("No JSON data received")
            return jsonify({"status": "error", "message": "No data received"}), 400
        
        # Log incoming alert
        logger.info(f"Received institutional alert: {json.dumps(alert_data, indent=2)}")
        
        # Process the signal using our strategy
        result = process_institutional_signal(alert_data)
        
        # Send Telegram notification
        if result.get("status") == "accepted":
            send_trade_notification(alert_data, result)
        
        # Log result
        logger.info(f"Processing result: {json.dumps(result, indent=2)}")
        
        return jsonify(result), 200
        
    except Exception as e:
        error_msg = f"Webhook processing error: {str(e)}"
        logger.error(error_msg)
        return jsonify({"status": "error", "message": error_msg}), 500

@app.route('/dashboard')
def dashboard():
    """Performance dashboard"""
    try:
        # This would integrate with your database to show performance metrics
        dashboard_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Institutional Strategy Dashboard</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; }
                .card { background: white; padding: 20px; margin: 20px 0; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .metric { display: inline-block; margin: 10px 20px; text-align: center; }
                .metric-value { font-size: 2em; font-weight: bold; color: #2c3e50; }
                .metric-label { color: #7f8c8d; }
                h1 { color: #2c3e50; text-align: center; }
                .status-active { color: #27ae60; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>📊 Institutional Strategy Dashboard</h1>
                
                <div class="card">
                    <h2>🎯 Strategy Status</h2>
                    <p class="status-active">✅ Active and Monitoring</p>
                    <p><strong>Strategy:</strong> Institutional Breakout</p>
                    <p><strong>Timeframe:</strong> 30m with 4h confirmation</p>
                    <p><strong>Risk per Trade:</strong> 2%</p>
                    <p><strong>Max Position Size:</strong> 15%</p>
                </div>
                
                <div class="card">
                    <h2>📈 Performance Metrics</h2>
                    <div class="metric">
                        <div class="metric-value">--</div>
                        <div class="metric-label">Total Trades</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">--</div>
                        <div class="metric-label">Win Rate</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">--</div>
                        <div class="metric-label">Profit Factor</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">--</div>
                        <div class="metric-label">Total P&L</div>
                    </div>
                </div>
                
                <div class="card">
                    <h2>⚙️ Configuration</h2>
                    <p><strong>Min Confidence:</strong> 80%</p>
                    <p><strong>Kelly Factor:</strong> 0.25</p>
                    <p><strong>Stop Loss:</strong> 2%</p>
                    <p><strong>Take Profit:</strong> 6%</p>
                    <p><strong>Max Daily Trades:</strong> 5</p>
                </div>
                
                <div class="card">
                    <h2>🔗 Integration Status</h2>
                    <p><strong>TradingView Webhook:</strong> ✅ Ready</p>
                    <p><strong>Database:</strong> ✅ Connected</p>
                    <p><strong>Telegram Notifications:</strong> {{ telegram_status }}</p>
                    <p><strong>Risk Management:</strong> ✅ Active</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        telegram_status = "✅ Enabled" if telegram.enabled else "⚠️ Disabled"
        return render_template_string(dashboard_html, telegram_status=telegram_status)
        
    except Exception as e:
        logger.error(f"Dashboard error: {str(e)}")
        return jsonify({"error": str(e)}), 500

def send_trade_notification(alert_data, result):
    """Send Telegram notification for trade execution"""
    try:
        symbol = alert_data.get('symbol', 'Unknown')
        signal = alert_data.get('signal', 'Unknown')
        price = alert_data.get('price', 0)
        confidence = alert_data.get('confidence', 0)
        position_size = result.get('position_size', 0)
        
        message = f"""
🚀 <b>Institutional Trade Signal</b>

📊 <b>Symbol:</b> {symbol}
📈 <b>Action:</b> {signal}
💰 <b>Price:</b> ₹{price}
📏 <b>Position Size:</b> {position_size:.2%}
🎯 <b>Confidence:</b> {confidence}%
⏰ <b>Time:</b> {datetime.now().strftime('%H:%M:%S')}

🔥 <b>Strategy:</b> Institutional Breakout
✅ <b>Status:</b> Signal Accepted

💡 <i>Automated execution in progress...</i>
        """
        
        telegram.send_message(message.strip())
        
    except Exception as e:
        logger.error(f"Notification error: {str(e)}")

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({"error": "Endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    logger.info("Starting OpenAlgo Institutional Strategy App...")
    logger.info(f"Telegram notifications: {'Enabled' if telegram.enabled else 'Disabled'}")
    
    # Get configuration from environment
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Server starting on {host}:{port}")
    app.run(host=host, port=port, debug=debug)
