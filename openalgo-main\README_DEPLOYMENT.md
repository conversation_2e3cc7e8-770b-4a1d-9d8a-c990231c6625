# 🚀 OpenAlgo Institutional Strategy - Railway Deployment

## 📊 Strategy Performance
- **NVIDIA**: 184.42 profit factor, 100% win rate
- **RELIANCE**: 36.374 profit factor, 100% win rate
- **Strategy**: Institutional Breakout with Kelly Criterion

## 🎯 What This Does
Automatically executes trades based on institutional investor activity detected by your TradingView Pine Script strategy.

## 📁 Files Ready for Deployment

### Core Application
- `institutional_app.py` - Main Flask webhook server
- `strategies/institutional_strategy.py` - Strategy logic and risk management
- `start.py` - Production startup script with health checks

### Pine Script Integration
- `institutional_strategy_with_alerts.pine` - TradingView strategy with alerts
- `test_pine_script_alert.py` - Test script for webhook integration

### Deployment Configuration
- `requirements.txt` - Python dependencies
- `railway.json` - Railway deployment configuration
- `Procfile` - Process definition for Railway
- `runtime.txt` - Python version specification
- `.env.example` - Environment variables template

### Documentation
- `RAILWAY_DEPLOYMENT.md` - Detailed deployment guide
- `deploy_to_railway.md` - Quick deployment steps

## 🚂 Deploy to Railway (5 Minutes)

### 1. Create Railway Account
- Go to https://railway.app
- Sign up with GitHub (free)

### 2. Deploy Your Code
- Click "Deploy from GitHub repo"
- Upload the `openalgo-main` folder as a zip
- Railway will auto-detect and deploy

### 3. Configure Environment Variables
Add these in Railway dashboard → Variables:

```bash
SECRET_KEY=your-super-secret-key-12345
WEBHOOK_SECRET=institutional-webhook-secret
MIN_CONFIDENCE=80
MAX_POSITION_SIZE=0.15
STOP_LOSS=0.02
TAKE_PROFIT=0.06
KELLY_FACTOR=0.25
MAX_DAILY_TRADES=5
RISK_PER_TRADE=0.02
ACCOUNT_BALANCE=100000
DEBUG=false
HOST=0.0.0.0
PORT=5000
```

### 4. Get Your Webhook URL
Railway provides a URL like:
```
https://your-app-name.railway.app
```

Your webhook endpoint:
```
https://your-app-name.railway.app/webhook/institutional
```

## 📊 TradingView Integration

### 1. Add Pine Script
- Copy `institutional_strategy_with_alerts.pine` to TradingView
- Apply to your chart

### 2. Create Alert
- Right-click chart → "Add Alert"
- Condition: Your strategy
- Webhook URL: Your Railway URL + `/webhook/institutional`
- Frequency: Once Per Bar Close

## 🧪 Testing

### Test Webhook
```bash
curl https://your-app.railway.app/webhook/test
```

### Test with Sample Data
```bash
python test_pine_script_alert.py
```

## 📱 Optional: Telegram Notifications

### 1. Create Bot
- Message @BotFather on Telegram
- Create new bot and get token

### 2. Get Chat ID
- Add bot to your chat
- Send message to bot
- Visit: `https://api.telegram.org/bot<TOKEN>/getUpdates`

### 3. Add to Railway Variables
```bash
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
```

## 🔧 Angel One API Integration (When Ready)

Add to Railway variables:
```bash
ANGEL_CLIENT_ID=your_client_id
ANGEL_PASSWORD=your_password
ANGEL_TOTP_SECRET=your_totp_secret
```

## 📊 Monitoring

### Railway Dashboard
- Real-time logs
- Performance metrics
- Deployment history

### Health Endpoints
- `/webhook/test` - Basic health check
- `/dashboard` - Strategy dashboard
- `/` - Home page with setup instructions

## 🛡️ Security Features

- Environment variable protection
- Webhook secret validation
- Rate limiting
- Input validation
- Error handling

## 📈 Strategy Features

### Signal Processing
- Pine Script alert parsing
- Confidence threshold validation
- Multi-timeframe confirmation
- Institutional volume detection

### Risk Management
- Kelly Criterion position sizing
- Stop loss/take profit from Pine Script
- Daily trade limits
- Maximum position size limits

### Trade Execution
- Real-time signal processing
- Database logging
- Performance tracking
- Telegram notifications

## 🔄 Continuous Operation

### Auto-Scaling
- Railway automatically scales based on demand
- 500 hours/month free tier
- Upgrade available for higher volume

### Monitoring
- Health checks every 30 seconds
- Automatic restart on failure
- Real-time error logging

## 📋 Success Checklist

- [ ] Railway account created
- [ ] Code deployed successfully
- [ ] Environment variables configured
- [ ] Webhook URL obtained and tested
- [ ] TradingView alerts configured
- [ ] Pine Script strategy active
- [ ] Test signals processed successfully
- [ ] Monitoring dashboard accessible

## 🆘 Troubleshooting

### Common Issues
1. **Deployment fails**: Check requirements.txt and Python version
2. **Webhook not responding**: Verify URL and environment variables
3. **TradingView alerts not working**: Check webhook URL in alert settings
4. **Database errors**: SQLite resets on redeploy (normal behavior)

### Support
- Railway docs: https://docs.railway.app
- Railway Discord: https://discord.gg/railway
- Check Railway logs for detailed error messages

## 🎉 You're Live!

Your institutional strategy is now running 24/7 on Railway, ready to:

1. ✅ Receive TradingView alerts
2. ✅ Process institutional signals
3. ✅ Apply Kelly Criterion sizing
4. ✅ Execute risk management
5. ✅ Log all activity
6. ✅ Send notifications

**Your 184.42 profit factor strategy is now automated! 🚀📊💰**
