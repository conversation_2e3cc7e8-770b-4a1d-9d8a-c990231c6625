//@version=6
strategy("Polygon MATIC Cutting-Edge Strategy",
         shorttitle="MATIC-CE",
         overlay=true,
         initial_capital=10000,
         default_qty_type=strategy.percent_of_equity,
         default_qty_value=25,
         commission_type=strategy.commission.percent,
         commission_value=0.1,
         slippage=3,
         max_bars_back=1000,
         calc_on_every_tick=true)

// ═══════════════════════════════════════════════════════════════════════════════════════
// STRATEGY DESCRIPTION - CUTTING-EDGE CRYPTO STRATEGY
// ═══════════════════════════════════════════════════════════════════════════════════════
// This breakthrough strategy is specifically designed for Polygon/MATIC crypto trading
// with advanced signal alternation to prevent duplicate signals and cutting-edge
// crypto market analysis techniques.
//
// Key Breakthrough Features:
// 1. SIGNAL ALTERNATION - Ensures buy/sell signals alternate perfectly
// 2. CRYPTO SMART MONEY - Whale movement and institutional crypto detection
// 3. DEFI METRICS - Layer 2 and DeFi ecosystem analysis
// 4. ADVANCED MOMENTUM - Multi-timeframe crypto momentum analysis
// 5. LIQUIDITY ZONES - Crypto-specific support/resistance detection
// 6. VOLATILITY ADAPTATION - Dynamic crypto volatility management
// ═══════════════════════════════════════════════════════════════════════════════════════

// SIGNAL ALTERNATION SYSTEM (Prevents Duplicate Signals)
// ═══════════════════════════════════════════════════════════════════════════════════════
var bool last_signal_was_buy = false
var bool signal_alternation_enabled = true

// INPUT PARAMETERS - CUTTING-EDGE CRYPTO ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Signal Alternation Control
enable_alternation = input.bool(true, "Enable Signal Alternation", tooltip="Ensures buy/sell signals alternate to prevent duplicates", group="Signal Control")
signal_strength_threshold = input.float(3.5, "Minimum Signal Strength", minval=2.0, maxval=10.0, step=0.1, tooltip="Higher values = fewer but stronger signals", group="Signal Control")
enable_debug_mode = input.bool(true, "Enable Debug Mode", tooltip="Shows signal strength values for debugging", group="Signal Control")
enable_simple_mode = input.bool(true, "Enable Simple Signal Mode", tooltip="Allows basic trend signals when advanced signals don't trigger", group="Signal Control")

// Crypto Smart Money Detection
whale_volume_multiplier = input.float(2.5, "Whale Volume Multiplier", minval=1.5, maxval=6.0, step=0.1, group="Crypto Smart Money")
institutional_flow_length = input.int(21, "Institutional Flow Length", minval=10, maxval=50, group="Crypto Smart Money")
smart_money_threshold = input.float(0.6, "Smart Money Threshold", minval=0.4, maxval=1.0, step=0.05, group="Crypto Smart Money")

// DeFi & Layer 2 Analysis
defi_correlation_length = input.int(14, "DeFi Correlation Length", minval=7, maxval=30, group="DeFi Analysis")
layer2_momentum_period = input.int(12, "Layer 2 Momentum Period", minval=5, maxval=25, group="DeFi Analysis")
ecosystem_strength_threshold = input.float(0.5, "Ecosystem Strength Threshold", minval=0.3, maxval=0.9, step=0.05, group="DeFi Analysis")

// Advanced Momentum System
momentum_fast = input.int(8, "Fast Momentum Period", minval=3, maxval=15, group="Advanced Momentum")
momentum_slow = input.int(21, "Slow Momentum Period", minval=15, maxval=35, group="Advanced Momentum")
momentum_signal = input.int(9, "Momentum Signal Period", minval=5, maxval=15, group="Advanced Momentum")
trend_strength_multiplier = input.float(1.8, "Trend Strength Multiplier", minval=1.2, maxval=2.5, step=0.1, group="Advanced Momentum")

// Crypto Volatility Management
volatility_lookback = input.int(20, "Volatility Lookback", minval=10, maxval=40, group="Volatility Management")
volatility_threshold_high = input.float(2.5, "High Volatility Threshold", minval=1.5, maxval=4.0, step=0.1, group="Volatility Management")
volatility_threshold_low = input.float(0.8, "Low Volatility Threshold", minval=0.5, maxval=1.2, step=0.1, group="Volatility Management")

// Liquidity Zone Detection
liquidity_zone_length = input.int(50, "Liquidity Zone Length", minval=20, maxval=100, group="Liquidity Zones")
support_resistance_strength = input.int(3, "S/R Strength", minval=2, maxval=8, group="Liquidity Zones")
breakout_confirmation_bars = input.int(2, "Breakout Confirmation Bars", minval=1, maxval=5, group="Liquidity Zones")

// Multi-Timeframe Analysis
enable_mtf = input.bool(false, "Enable Multi-Timeframe", group="Multi-Timeframe")
higher_timeframe = input.timeframe("240", "Higher Timeframe", group="Multi-Timeframe")
mtf_trend_filter = input.bool(false, "MTF Trend Filter", group="Multi-Timeframe")

// Risk Management
dynamic_position_sizing = input.bool(true, "Dynamic Position Sizing", group="Risk Management")
base_stop_loss = input.float(3.5, "Base Stop Loss %", minval=1.0, maxval=8.0, step=0.1, group="Risk Management")
base_take_profit = input.float(7.0, "Base Take Profit %", minval=2.0, maxval=15.0, step=0.5, group="Risk Management")
max_position_size = input.float(35.0, "Max Position Size %", minval=10.0, maxval=50.0, step=1.0, group="Risk Management")

// ═══════════════════════════════════════════════════════════════════════════════════════
// CRYPTO MARKET STRUCTURE ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Crypto Volatility Analysis (Enhanced for crypto markets)
atr_crypto = ta.atr(volatility_lookback)
atr_avg = ta.sma(atr_crypto, volatility_lookback)
volatility_ratio = atr_crypto / atr_avg

// Crypto Market Regime Detection
high_volatility_regime = volatility_ratio > volatility_threshold_high
low_volatility_regime = volatility_ratio < volatility_threshold_low
normal_volatility_regime = not high_volatility_regime and not low_volatility_regime

// Crypto Volume Analysis (Whale Detection)
volume_ma = ta.sma(volume, 20)
volume_std = ta.stdev(volume, 20)
whale_volume_threshold = volume_ma + (volume_std * whale_volume_multiplier)
whale_activity = volume > whale_volume_threshold

// Smart Money Flow (Crypto Institutional Detection)
typical_price = hlc3
money_flow_raw = typical_price * volume
positive_flow = close > close[1] ? money_flow_raw : 0
negative_flow = close < close[1] ? money_flow_raw : 0

positive_flow_sum = ta.sma(positive_flow, institutional_flow_length)
negative_flow_sum = ta.sma(negative_flow, institutional_flow_length)
smart_money_ratio = positive_flow_sum / (positive_flow_sum + negative_flow_sum)

smart_money_bullish = smart_money_ratio > (0.5 + smart_money_threshold/2)
smart_money_bearish = smart_money_ratio < (0.5 - smart_money_threshold/2)

// ═══════════════════════════════════════════════════════════════════════════════════════
// DEFI & LAYER 2 ECOSYSTEM ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════

// DeFi Ecosystem Strength (Proxy using price action and volume)
price_momentum_short = (close - close[defi_correlation_length]) / close[defi_correlation_length] * 100
volume_momentum = (volume - ta.sma(volume, defi_correlation_length)) / ta.sma(volume, defi_correlation_length) * 100

// Layer 2 Momentum (Polygon-specific analysis)
layer2_price_strength = ta.rsi(close, layer2_momentum_period)
layer2_volume_strength = ta.rsi(volume, layer2_momentum_period)
layer2_combined_strength = (layer2_price_strength + layer2_volume_strength) / 2

// Ecosystem Health Score
ecosystem_score = (price_momentum_short > 0 ? 0.4 : 0) + (volume_momentum > 0 ? 0.3 : 0) + (layer2_combined_strength > 50 ? 0.3 : 0)

ecosystem_bullish = ecosystem_score >= ecosystem_strength_threshold
ecosystem_bearish = ecosystem_score <= (1 - ecosystem_strength_threshold)

// ═══════════════════════════════════════════════════════════════════════════════════════
// ADVANCED MOMENTUM SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════════════

// Multi-layered Momentum Analysis
momentum_fast_val = ta.rsi(close, momentum_fast)
momentum_slow_val = ta.rsi(close, momentum_slow)
momentum_signal_val = ta.sma(momentum_fast_val, momentum_signal)

// Momentum Convergence/Divergence
momentum_bullish_cross = ta.crossover(momentum_fast_val, momentum_signal_val) and momentum_fast_val > 50
momentum_bearish_cross = ta.crossunder(momentum_fast_val, momentum_signal_val) and momentum_fast_val < 50

// Trend Strength Analysis
price_ma_fast = ta.ema(close, 12)
price_ma_slow = ta.ema(close, 26)
trend_strength = math.abs(price_ma_fast - price_ma_slow) / price_ma_slow * 100

strong_uptrend = price_ma_fast > price_ma_slow and trend_strength > trend_strength_multiplier
strong_downtrend = price_ma_fast < price_ma_slow and trend_strength > trend_strength_multiplier

// ═══════════════════════════════════════════════════════════════════════════════════════
// LIQUIDITY ZONE DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Dynamic Support/Resistance Levels
highest_high = ta.highest(high, liquidity_zone_length)
lowest_low = ta.lowest(low, liquidity_zone_length)

// Liquidity Zone Breaks
bullish_liquidity_break = ta.crossover(close, highest_high[support_resistance_strength])
bearish_liquidity_break = ta.crossunder(close, lowest_low[support_resistance_strength])

// Volume Confirmation for Breaks
volume_confirmation = volume > ta.sma(volume, 10) * 1.5

confirmed_bullish_break = bullish_liquidity_break and volume_confirmation
confirmed_bearish_break = bearish_liquidity_break and volume_confirmation

// ═══════════════════════════════════════════════════════════════════════════════════════
// MULTI-TIMEFRAME ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Higher Timeframe Trend
htf_close = request.security(syminfo.tickerid, higher_timeframe, close)
htf_ema_fast = request.security(syminfo.tickerid, higher_timeframe, ta.ema(close, 21))
htf_ema_slow = request.security(syminfo.tickerid, higher_timeframe, ta.ema(close, 50))

htf_bullish_trend = htf_close > htf_ema_fast and htf_ema_fast > htf_ema_slow
htf_bearish_trend = htf_close < htf_ema_fast and htf_ema_fast < htf_ema_slow

// MTF Confirmation
mtf_bullish_confirmation = not enable_mtf or not mtf_trend_filter or htf_bullish_trend
mtf_bearish_confirmation = not enable_mtf or not mtf_trend_filter or htf_bearish_trend

// ═══════════════════════════════════════════════════════════════════════════════════════
// CUTTING-EDGE SIGNAL GENERATION WITH ALTERNATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Signal Strength Calculation (0-10 scale)
bullish_signal_strength = 0.0
bearish_signal_strength = 0.0

// Add strength points for bullish signals
if momentum_bullish_cross
    bullish_signal_strength += 2.0
if smart_money_bullish
    bullish_signal_strength += 1.5
if ecosystem_bullish
    bullish_signal_strength += 1.0
if confirmed_bullish_break
    bullish_signal_strength += 1.5
if whale_activity and close > open
    bullish_signal_strength += 1.0
if strong_uptrend
    bullish_signal_strength += 1.5
if mtf_bullish_confirmation
    bullish_signal_strength += 1.0
if normal_volatility_regime
    bullish_signal_strength += 0.5
// Additional responsive signals
if close > price_ma_fast and price_ma_fast > price_ma_slow
    bullish_signal_strength += 0.8
if volume > volume_ma * 1.2
    bullish_signal_strength += 0.7
if layer2_combined_strength > 60
    bullish_signal_strength += 0.6

// Add strength points for bearish signals
if momentum_bearish_cross
    bearish_signal_strength += 2.0
if smart_money_bearish
    bearish_signal_strength += 1.5
if ecosystem_bearish
    bearish_signal_strength += 1.0
if confirmed_bearish_break
    bearish_signal_strength += 1.5
if whale_activity and close < open
    bearish_signal_strength += 1.0
if strong_downtrend
    bearish_signal_strength += 1.5
if mtf_bearish_confirmation
    bearish_signal_strength += 1.0
if normal_volatility_regime
    bearish_signal_strength += 0.5
// Additional responsive signals
if close < price_ma_fast and price_ma_fast < price_ma_slow
    bearish_signal_strength += 0.8
if volume > volume_ma * 1.2
    bearish_signal_strength += 0.7
if layer2_combined_strength < 40
    bearish_signal_strength += 0.6

// Signal Generation with Alternation Logic
strong_bullish_signal = bullish_signal_strength >= signal_strength_threshold
strong_bearish_signal = bearish_signal_strength >= signal_strength_threshold

// Fallback Simple Signals (when main signals don't trigger)
simple_bullish = momentum_bullish_cross and close > price_ma_fast and volume > volume_ma
simple_bearish = momentum_bearish_cross and close < price_ma_fast and volume > volume_ma

// Combined Signal Logic
bullish_trigger = strong_bullish_signal or (not strong_bullish_signal and not strong_bearish_signal and simple_bullish)
bearish_trigger = strong_bearish_signal or (not strong_bullish_signal and not strong_bearish_signal and simple_bearish)

// Alternation Logic - Ensures signals alternate
buy_signal = bullish_trigger and (not enable_alternation or not last_signal_was_buy)
sell_signal = bearish_trigger and (not enable_alternation or last_signal_was_buy)

// Update signal state
if buy_signal
    last_signal_was_buy := true
if sell_signal
    last_signal_was_buy := false

// ═══════════════════════════════════════════════════════════════════════════════════════
// DYNAMIC POSITION SIZING & RISK MANAGEMENT
// ═══════════════════════════════════════════════════════════════════════════════════════

// Volatility-Adjusted Position Sizing
volatility_adjustment = dynamic_position_sizing ? (high_volatility_regime ? 0.7 : low_volatility_regime ? 1.3 : 1.0) : 1.0

// Signal Strength Position Sizing
strength_multiplier_buy = bullish_signal_strength / 10.0
strength_multiplier_sell = bearish_signal_strength / 10.0

// Final Position Sizes
buy_position_size = math.min(25.0 * volatility_adjustment * strength_multiplier_buy, max_position_size)
sell_position_size = math.min(25.0 * volatility_adjustment * strength_multiplier_sell, max_position_size)

// Adaptive Stop Loss and Take Profit
adaptive_stop_loss = base_stop_loss * (high_volatility_regime ? 1.5 : low_volatility_regime ? 0.8 : 1.0)
adaptive_take_profit = base_take_profit * (high_volatility_regime ? 1.3 : low_volatility_regime ? 0.9 : 1.0)

// ═══════════════════════════════════════════════════════════════════════════════════════
// STRATEGY EXECUTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Calculate Stop Loss and Take Profit Levels
long_stop = close * (1 - adaptive_stop_loss / 100)
long_target = close * (1 + adaptive_take_profit / 100)
short_stop = close * (1 + adaptive_stop_loss / 100)
short_target = close * (1 - adaptive_take_profit / 100)

// Execute Buy Signals
if buy_signal and strategy.position_size == 0
    signal_comment = "BUY | Strength: " + str.tostring(math.round(bullish_signal_strength, 1)) + " | Size: " + str.tostring(math.round(buy_position_size, 1)) + "%" + (whale_activity ? " | WHALE" : "") + (ecosystem_bullish ? " | DeFi+" : "")
    
    strategy.entry("Long", strategy.long, qty=strategy.equity * buy_position_size / 100 / close, comment=signal_comment)
    strategy.exit("Long Exit", "Long", stop=long_stop, limit=long_target, comment="Long Exit")

// Execute Sell Signals
if sell_signal and strategy.position_size == 0
    signal_comment = "SELL | Strength: " + str.tostring(math.round(bearish_signal_strength, 1)) + " | Size: " + str.tostring(math.round(sell_position_size, 1)) + "%" + (whale_activity ? " | WHALE" : "") + (ecosystem_bearish ? " | DeFi-" : "")

    strategy.entry("Short", strategy.short, qty=strategy.equity * sell_position_size / 100 / close, comment=signal_comment)
    strategy.exit("Short Exit", "Short", stop=short_stop, limit=short_target, comment="Short Exit")

// ═══════════════════════════════════════════════════════════════════════════════════════
// CUTTING-EDGE VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot Dynamic Support/Resistance Levels
plot(highest_high, "Resistance Zone", color=color.red, linewidth=2, style=plot.style_stepline)
plot(lowest_low, "Support Zone", color=color.green, linewidth=2, style=plot.style_stepline)

// Plot Moving Averages
plot(price_ma_fast, "Fast EMA", color=color.blue, linewidth=1)
plot(price_ma_slow, "Slow EMA", color=color.orange, linewidth=1)

// Background Colors for Market Regimes
bgcolor(high_volatility_regime ? color.new(color.red, 95) : na, title="High Volatility")
bgcolor(low_volatility_regime ? color.new(color.green, 95) : na, title="Low Volatility")
bgcolor(normal_volatility_regime ? color.new(color.blue, 98) : na, title="Normal Volatility")

// Background Colors for Ecosystem Health
bgcolor(ecosystem_bullish ? color.new(color.lime, 97) : na, title="DeFi Bullish")
bgcolor(ecosystem_bearish ? color.new(color.red, 97) : na, title="DeFi Bearish")

// Background Colors for Smart Money
bgcolor(smart_money_bullish ? color.new(color.aqua, 95) : na, title="Smart Money Bullish")
bgcolor(smart_money_bearish ? color.new(color.purple, 95) : na, title="Smart Money Bearish")

// Signal Visualization
plotshape(buy_signal and strong_bullish_signal, "STRONG BUY", shape.triangleup, location.belowbar,
          color=color.lime, size=size.large, text="BUY")
plotshape(sell_signal and strong_bearish_signal, "STRONG SELL", shape.triangledown, location.abovebar,
          color=color.red, size=size.large, text="SELL")
plotshape(buy_signal and not strong_bullish_signal, "SIMPLE BUY", shape.circle, location.belowbar,
          color=color.green, size=size.small, text="B")
plotshape(sell_signal and not strong_bearish_signal, "SIMPLE SELL", shape.circle, location.abovebar,
          color=color.maroon, size=size.small, text="S")

// Whale Activity Markers
plotchar(whale_activity, "Whale Activity", "🐋", location.top, color=color.purple, size=size.small)

// Liquidity Break Markers
plotchar(confirmed_bullish_break, "Bull Break", "▲", location.bottom, color=color.lime, size=size.tiny)
plotchar(confirmed_bearish_break, "Bear Break", "▼", location.top, color=color.red, size=size.tiny)

// Momentum Signals
plotchar(momentum_bullish_cross, "Mom Bull", "↗", location.bottom, color=color.blue, size=size.tiny)
plotchar(momentum_bearish_cross, "Mom Bear", "↘", location.top, color=color.blue, size=size.tiny)

// Debug Mode - Show Signal Strength Values
if enable_debug_mode and barstate.isconfirmed
    if bullish_signal_strength > 3.0 or bearish_signal_strength > 3.0
        debug_text = "B:" + str.tostring(math.round(bullish_signal_strength, 1)) + " S:" + str.tostring(math.round(bearish_signal_strength, 1))
        label.new(bar_index, high, debug_text, style=label.style_label_down, color=color.yellow, textcolor=color.black, size=size.small)

// Signal Strength Indicator
var table signal_table = table.new(position.top_right, 2, 5, bgcolor=color.white, border_width=1)
if barstate.islast or barstate.isrealtime
    table.cell(signal_table, 0, 0, "Signal Monitor", text_color=color.black, text_size=size.small, bgcolor=color.gray)
    table.cell(signal_table, 1, 0, "Value", text_color=color.black, text_size=size.small, bgcolor=color.gray)
    table.cell(signal_table, 0, 1, "Threshold", text_color=color.black, text_size=size.small)
    table.cell(signal_table, 1, 1, str.tostring(signal_strength_threshold), text_color=color.blue, text_size=size.small)
    table.cell(signal_table, 0, 2, "Bullish", text_color=color.green, text_size=size.small)
    table.cell(signal_table, 1, 2, str.tostring(math.round(bullish_signal_strength, 1)), text_color=bullish_signal_strength >= signal_strength_threshold ? color.green : color.gray, text_size=size.small)
    table.cell(signal_table, 0, 3, "Bearish", text_color=color.red, text_size=size.small)
    table.cell(signal_table, 1, 3, str.tostring(math.round(bearish_signal_strength, 1)), text_color=bearish_signal_strength >= signal_strength_threshold ? color.red : color.gray, text_size=size.small)
    table.cell(signal_table, 0, 4, "Last Signal", text_color=color.black, text_size=size.small)
    table.cell(signal_table, 1, 4, last_signal_was_buy ? "BUY" : "SELL", text_color=last_signal_was_buy ? color.green : color.red, text_size=size.small)

// ═══════════════════════════════════════════════════════════════════════════════════════
// CUTTING-EDGE ALERT SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════════════

// Primary Trading Alerts
if buy_signal and not buy_signal[1]
    alert("🚀 POLYGON MATIC BUY SIGNAL | Strength: " + str.tostring(math.round(bullish_signal_strength, 1)) + " | " + (whale_activity ? "WHALE DETECTED | " : "") + (ecosystem_bullish ? "DeFi BULLISH | " : "") + "Size: " + str.tostring(math.round(buy_position_size, 1)) + "%", alert.freq_once_per_bar)

if sell_signal and not sell_signal[1]
    alert("📉 POLYGON MATIC SELL SIGNAL | Strength: " + str.tostring(math.round(bearish_signal_strength, 1)) + " | " + (whale_activity ? "WHALE DETECTED | " : "") + (ecosystem_bearish ? "DeFi BEARISH | " : "") + "Size: " + str.tostring(math.round(sell_position_size, 1)) + "%", alert.freq_once_per_bar)

// Whale Activity Alerts
if whale_activity and not whale_activity[1]
    alert("🐋 WHALE ACTIVITY DETECTED on MATIC | Volume: " + str.tostring(math.round(volume/1000, 1)) + "K | Price Impact: " + str.tostring(math.round((close-open)/open*100, 2)) + "%", alert.freq_once_per_bar)

// Ecosystem Health Alerts
if ecosystem_bullish and not ecosystem_bullish[1]
    alert("🌟 POLYGON ECOSYSTEM BULLISH | DeFi metrics improving | Score: " + str.tostring(math.round(ecosystem_score*100, 1)) + "%", alert.freq_once_per_bar)

if ecosystem_bearish and not ecosystem_bearish[1]
    alert("⚠️ POLYGON ECOSYSTEM BEARISH | DeFi metrics declining | Score: " + str.tostring(math.round(ecosystem_score*100, 1)) + "%", alert.freq_once_per_bar)

// Liquidity Break Alerts
if confirmed_bullish_break and not confirmed_bullish_break[1]
    alert("💥 BULLISH LIQUIDITY BREAK on MATIC | Resistance broken with volume confirmation", alert.freq_once_per_bar)

if confirmed_bearish_break and not confirmed_bearish_break[1]
    alert("💥 BEARISH LIQUIDITY BREAK on MATIC | Support broken with volume confirmation", alert.freq_once_per_bar)

// Smart Money Alerts
if smart_money_bullish and not smart_money_bullish[1]
    alert("💰 SMART MONEY BULLISH on MATIC | Institutional flow detected | Ratio: " + str.tostring(math.round(smart_money_ratio*100, 1)) + "%", alert.freq_once_per_bar)

if smart_money_bearish and not smart_money_bearish[1]
    alert("💰 SMART MONEY BEARISH on MATIC | Institutional outflow detected | Ratio: " + str.tostring(math.round(smart_money_ratio*100, 1)) + "%", alert.freq_once_per_bar)

// Volatility Regime Alerts
if high_volatility_regime and not high_volatility_regime[1]
    alert("⚡ HIGH VOLATILITY REGIME on MATIC | Reduce position sizes | Ratio: " + str.tostring(math.round(volatility_ratio, 2)), alert.freq_once_per_bar)

if low_volatility_regime and not low_volatility_regime[1]
    alert("😴 LOW VOLATILITY REGIME on MATIC | Consider larger positions | Ratio: " + str.tostring(math.round(volatility_ratio, 2)), alert.freq_once_per_bar)

// Multi-Timeframe Alerts
if enable_mtf and htf_bullish_trend and not htf_bullish_trend[1]
    alert("📈 HIGHER TIMEFRAME BULLISH on MATIC | " + higher_timeframe + " trend confirmed", alert.freq_once_per_bar)

if enable_mtf and htf_bearish_trend and not htf_bearish_trend[1]
    alert("📉 HIGHER TIMEFRAME BEARISH on MATIC | " + higher_timeframe + " trend confirmed", alert.freq_once_per_bar)

// ═══════════════════════════════════════════════════════════════════════════════════════
// STRATEGY PERFORMANCE METRICS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Performance Table
var table perf_table = table.new(position.bottom_right, 2, 6, bgcolor=color.white, border_width=1)
if barstate.islast or barstate.isrealtime
    table.cell(perf_table, 0, 0, "Performance", text_color=color.black, text_size=size.small, bgcolor=color.gray)
    table.cell(perf_table, 1, 0, "Value", text_color=color.black, text_size=size.small, bgcolor=color.gray)

    table.cell(perf_table, 0, 1, "Total Trades", text_color=color.black, text_size=size.small)
    table.cell(perf_table, 1, 1, str.tostring(strategy.closedtrades), text_color=color.black, text_size=size.small)

    table.cell(perf_table, 0, 2, "Win Rate", text_color=color.black, text_size=size.small)
    win_rate = strategy.closedtrades > 0 ? strategy.wintrades / strategy.closedtrades * 100 : 0
    table.cell(perf_table, 1, 2, str.tostring(math.round(win_rate, 1)) + "%", text_color=win_rate > 50 ? color.green : color.red, text_size=size.small)

    table.cell(perf_table, 0, 3, "Profit Factor", text_color=color.black, text_size=size.small)
    table.cell(perf_table, 1, 3, str.tostring(math.round(strategy.grossprofit/math.max(strategy.grossloss, 1), 2)), text_color=strategy.grossprofit > strategy.grossloss ? color.green : color.red, text_size=size.small)

    table.cell(perf_table, 0, 4, "Net Profit", text_color=color.black, text_size=size.small)
    table.cell(perf_table, 1, 4, str.tostring(math.round(strategy.netprofit, 2)), text_color=strategy.netprofit > 0 ? color.green : color.red, text_size=size.small)

    table.cell(perf_table, 0, 5, "Alternation", text_color=color.black, text_size=size.small)
    table.cell(perf_table, 1, 5, enable_alternation ? "ON" : "OFF", text_color=enable_alternation ? color.green : color.orange, text_size=size.small)
