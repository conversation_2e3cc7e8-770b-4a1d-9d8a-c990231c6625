{% extends "layout.html" %}

{% block title %}OpenAlgo Downloads{% endblock %}

{% block head %}
<!-- Additional head content specific to download page -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Platform tabs functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Update active tab
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('tab-active'));
                tab.classList.add('tab-active');

                // Show corresponding downloads
                const platform = tab.dataset.platform;
                document.querySelectorAll('[id$="Downloads"]').forEach(div => div.classList.add('hidden'));
                document.getElementById(`${platform}Downloads`).classList.remove('hidden');
            });
        });
    });
</script>
{% endblock %}

{% block content %}
<h1 class="text-4xl font-bold text-center mb-8">FastScalper <span class="text-primary">Desktop</span></h1>

<div class="card bg-base-100 shadow-xl">
    <div class="card-body">
        <!-- Version Selector -->
        <div class="flex justify-between items-center mb-6">
            <h2 class="card-title">Available Downloads</h2>
            <select class="select select-bordered w-full max-w-xs" id="versionSelect">
                <option value="0.1.0">Version 0.1.0</option>
            </select>
        </div>

        <!-- Platform Tabs -->
        <div class="tabs tabs-boxed mb-6">
            <a class="tab tab-active" data-platform="mac">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83"/>
                </svg>
                macOS
            </a>
            <a class="tab" data-platform="linux">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20zm0 18a8 8 0 1 1 0-16 8 8 0 0 1 0 16z"/>
                </svg>
                Linux
            </a>
            <a class="tab" data-platform="windows">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M3 12V6.75L9 5.43V11.91L3 12M20 3V11.75L10 11.9V5.21L20 3M3 13L9 13.09V19.9L3 18.75V13M20 13.25V22L10 20.09V13.1L20 13.25Z"/>
                </svg>
                Windows
            </a>
        </div>

        <!-- Download Tables -->
        <div id="macDownloads" class="overflow-x-auto">
            <table class="table w-full">
                <thead>
                    <tr>
                        <th>Platform</th>
                        <th>Version</th>
                        <th>Download</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Mac Universal</td>
                        <td>v0.1.0</td>
                        <td>
                            <a href="https://github.com/marketcalls/fastscalper-tauri/releases/download/v0.1.0/fastscalper_0.1.0_universal.dmg" class="btn btn-sm btn-primary">
                                Download DMG
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td>Mac Universal (Portable)</td>
                        <td>v0.1.0</td>
                        <td>
                            <a href="https://github.com/marketcalls/fastscalper-tauri/releases/download/v0.1.0/fastscalper_0.1.0_universal_mac.zip" class="btn btn-sm btn-primary">
                                Download ZIP
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div id="linuxDownloads" class="overflow-x-auto hidden">
            <table class="table w-full">
                <thead>
                    <tr>
                        <th>Platform</th>
                        <th>Version</th>
                        <th>Download</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Ubuntu / Debian</td>
                        <td>v0.1.0</td>
                        <td>
                            <a href="https://github.com/marketcalls/fastscalper-tauri/releases/download/v0.1.0/fastscalper_0.1.0_amd64.deb" class="btn btn-sm btn-primary">
                                Download DEB
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td>Fedora / Red Hat</td>
                        <td>v0.1.0</td>
                        <td>
                            <a href="https://github.com/marketcalls/fastscalper-tauri/releases/download/v0.1.0/fastscalper-0.1.0-1.x86_64.rpm" class="btn btn-sm btn-primary">
                                Download RPM
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td>AppImage</td>
                        <td>v0.1.0</td>
                        <td>
                            <a href="https://github.com/marketcalls/fastscalper-tauri/releases/download/v0.1.0/fastscalper_0.1.0_amd64.AppImage" class="btn btn-sm btn-primary">
                                Download AppImage
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div id="windowsDownloads" class="overflow-x-auto hidden">
            <table class="table w-full">
                <thead>
                    <tr>
                        <th>Platform</th>
                        <th>Version</th>
                        <th>Download</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Windows (MSI)</td>
                        <td>v0.1.0</td>
                        <td>
                            <a href="https://github.com/marketcalls/fastscalper-tauri/releases/download/v0.1.0/fastscalper_0.1.0_x64_en-US.msi" class="btn btn-sm btn-primary">
                                Download MSI
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td>Windows (EXE)</td>
                        <td>v0.1.0</td>
                        <td>
                            <a href="https://github.com/marketcalls/fastscalper-tauri/releases/download/v0.1.0/fastscalper_0.1.0_x64-setup.exe" class="btn btn-sm btn-primary">
                                Download EXE
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
