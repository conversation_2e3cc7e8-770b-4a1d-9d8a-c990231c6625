"""
Institutional Breakout Strategy for OpenAlgo
============================================

This strategy implements institutional investor detection and automated trading
based on volume breakouts and multi-timeframe confirmation.

Features:
- Kelly Criterion position sizing
- Multi-timeframe confirmation
- Risk management with stop-loss and take-profit
- Institutional volume detection
- Real-time signal processing

Author: OpenAlgo Institutional Strategy
Version: 1.0
"""

import os
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple, Optional
import pandas as pd
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InstitutionalStrategy:
    """
    Institutional Breakout Strategy Implementation
    """
    
    def __init__(self):
        """Initialize strategy configuration"""
        self.config = {
            "name": "Institutional Breakout Strategy",
            "timeframe": "30m",
            "confirmation_timeframe": "4h",
            "min_confidence": int(os.getenv("MIN_CONFIDENCE", 80)),
            "max_position_size": float(os.getenv("MAX_POSITION_SIZE", 0.15)),
            "stop_loss": float(os.getenv("STOP_LOSS", 0.02)),
            "take_profit": float(os.getenv("TAKE_PROFIT", 0.06)),
            "kelly_factor": float(os.getenv("KELLY_FACTOR", 0.25)),
            "max_daily_trades": int(os.getenv("MAX_DAILY_TRADES", 5)),
            "risk_per_trade": float(os.getenv("RISK_PER_TRADE", 0.02)),
            "max_correlation": float(os.getenv("MAX_CORRELATION", 0.7)),
            "max_sector_exposure": float(os.getenv("MAX_SECTOR_EXPOSURE", 0.25))
        }
        
        self.db_path = "openalgo.db"
        self.initialize_database()
    
    def initialize_database(self):
        """Initialize database tables for strategy tracking"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create trades table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS institutional_trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    symbol TEXT NOT NULL,
                    signal TEXT NOT NULL,
                    price REAL NOT NULL,
                    quantity INTEGER NOT NULL,
                    confidence INTEGER NOT NULL,
                    strategy TEXT DEFAULT 'institutional_breakout',
                    status TEXT DEFAULT 'pending',
                    pnl REAL DEFAULT 0.0,
                    entry_price REAL,
                    exit_price REAL,
                    stop_loss REAL,
                    take_profit REAL
                )
            """)
            
            # Create performance tracking table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE DEFAULT CURRENT_DATE,
                    total_trades INTEGER DEFAULT 0,
                    winning_trades INTEGER DEFAULT 0,
                    total_pnl REAL DEFAULT 0.0,
                    max_drawdown REAL DEFAULT 0.0,
                    win_rate REAL DEFAULT 0.0,
                    profit_factor REAL DEFAULT 0.0
                )
            """)
            
            conn.commit()
            conn.close()
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Database initialization error: {str(e)}")
    
    def calculate_position_size(self, confidence: int, account_balance: float,
                              current_price: float, pine_script_size: float = None) -> float:
        """
        Calculate optimal position size using Kelly Criterion from Pine Script or fallback calculation

        Args:
            confidence: Signal confidence (0-100)
            account_balance: Current account balance
            current_price: Current stock price
            pine_script_size: Position size calculated by Pine Script (optional)

        Returns:
            Position size as percentage of account balance
        """
        try:
            # Use Pine Script calculated position size if available
            if pine_script_size is not None and pine_script_size > 0:
                position_size = pine_script_size / 100.0  # Convert percentage to decimal
                logger.info(f"📊 Using Pine Script position size: {position_size:.4f} ({pine_script_size}%)")
            else:
                # Fallback to our Kelly Criterion calculation
                base_risk = self.config["risk_per_trade"]

                # Confidence multiplier (0.8 to 1.2 based on confidence)
                confidence_multiplier = 0.8 + (confidence - 80) * 0.004
                confidence_multiplier = max(0.8, min(1.2, confidence_multiplier))

                # Kelly Criterion adjustment
                kelly_multiplier = self.config["kelly_factor"]

                # Calculate position size
                position_size = base_risk * confidence_multiplier * kelly_multiplier

                logger.info(f"📊 Calculated position size: {position_size:.4f} "
                           f"(confidence: {confidence}, multiplier: {confidence_multiplier:.2f})")

            # Apply maximum position size limit
            max_size = self.config["max_position_size"]
            position_size = min(position_size, max_size)

            logger.info(f"✅ Final position size: {position_size:.4f} (max: {max_size})")

            return position_size

        except Exception as e:
            logger.error(f"Position size calculation error: {str(e)}")
            return self.config["risk_per_trade"]  # Fallback to base risk
    
    def validate_signal(self, alert_data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate incoming trading signal from our proven Pine Script strategy

        Args:
            alert_data: Dictionary containing signal data from TradingView Pine Script

        Returns:
            Tuple of (is_valid, message)
        """
        try:
            # Required fields validation for Pine Script alerts
            required_fields = ['symbol', 'signal', 'confidence', 'price', 'timeframe']

            for field in required_fields:
                if field not in alert_data:
                    return False, f"Missing required field: {field}"

            # Confidence threshold validation
            confidence = int(alert_data.get('confidence', 0))
            if confidence < self.config["min_confidence"]:
                return False, f"Confidence {confidence}% below threshold {self.config['min_confidence']}%"

            # Signal type validation
            signal = alert_data.get('signal', '').upper()
            if signal not in ['BUY', 'SELL']:
                return False, f"Invalid signal type: {signal}"

            # Price validation
            price = float(alert_data.get('price', 0))
            if price <= 0:
                return False, "Invalid price value"

            # Validate Pine Script strategy confirmation signals
            block_trade = alert_data.get('block_trade_detected', False)
            institutional_volume = alert_data.get('institutional_volume', False)
            mtf_confirmed = alert_data.get('mtf_confirmed', False)

            # Ensure our Pine Script confirmations are present
            if not (block_trade or institutional_volume):
                return False, "Missing institutional confirmation signals"

            # Daily trade limit check
            if self.exceeds_daily_limit():
                return False, "Daily trade limit exceeded"

            logger.info(f"✅ Pine Script signal validation PASSED for {alert_data.get('symbol')}")
            logger.info(f"   Confidence: {confidence}% | Block Trade: {block_trade} | Institutional: {institutional_volume} | MTF: {mtf_confirmed}")
            return True, "Pine Script signal validated successfully"

        except Exception as e:
            logger.error(f"Signal validation error: {str(e)}")
            return False, f"Validation error: {str(e)}"
    
    def exceeds_daily_limit(self) -> bool:
        """Check if daily trade limit is exceeded"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            today = datetime.now().date()
            cursor.execute("""
                SELECT COUNT(*) FROM institutional_trades 
                WHERE DATE(timestamp) = ? AND status = 'executed'
            """, (today,))
            
            daily_trades = cursor.fetchone()[0]
            conn.close()
            
            return daily_trades >= self.config["max_daily_trades"]
            
        except Exception as e:
            logger.error(f"Daily limit check error: {str(e)}")
            return False
    
    def get_account_balance(self) -> float:
        """Get current account balance (placeholder - integrate with broker API)"""
        # This should be integrated with your broker's API
        # For now, return a default value
        return float(os.getenv("ACCOUNT_BALANCE", 100000.0))
    
    def calculate_stop_loss_take_profit(self, entry_price: float,
                                      signal: str, pine_script_sl: float = None,
                                      pine_script_tp: float = None) -> Tuple[float, float]:
        """
        Calculate stop loss and take profit levels using Pine Script values or fallback

        Args:
            entry_price: Entry price for the trade
            signal: BUY or SELL signal
            pine_script_sl: Stop loss percentage from Pine Script
            pine_script_tp: Take profit percentage from Pine Script

        Returns:
            Tuple of (stop_loss, take_profit)
        """
        try:
            # Use Pine Script values if available
            if pine_script_sl is not None and pine_script_tp is not None:
                stop_loss_pct = pine_script_sl / 100.0
                take_profit_pct = pine_script_tp / 100.0
                logger.info(f"📊 Using Pine Script SL: {pine_script_sl}%, TP: {pine_script_tp}%")
            else:
                # Fallback to configuration values
                stop_loss_pct = self.config["stop_loss"] / 100.0
                take_profit_pct = self.config["take_profit"] / 100.0
                logger.info(f"📊 Using config SL: {self.config['stop_loss']}%, TP: {self.config['take_profit']}%")

            if signal.upper() == "BUY":
                stop_loss = entry_price * (1 - stop_loss_pct)
                take_profit = entry_price * (1 + take_profit_pct)
            else:  # SELL
                stop_loss = entry_price * (1 + stop_loss_pct)
                take_profit = entry_price * (1 - take_profit_pct)

            logger.info(f"✅ SL: ₹{stop_loss:.2f}, TP: ₹{take_profit:.2f}")
            return stop_loss, take_profit

        except Exception as e:
            logger.error(f"Stop loss/take profit calculation error: {str(e)}")
            return entry_price * 0.98, entry_price * 1.06  # Default values
    
    def log_trade(self, alert_data: Dict[str, Any], position_size: float, 
                  status: str = "pending") -> int:
        """
        Log trade to database
        
        Args:
            alert_data: Signal data
            position_size: Calculated position size
            status: Trade status
            
        Returns:
            Trade ID
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            entry_price = float(alert_data['price'])
            pine_script_sl = alert_data.get('stop_loss')
            pine_script_tp = alert_data.get('take_profit')
            stop_loss, take_profit = self.calculate_stop_loss_take_profit(
                entry_price, alert_data['signal'], pine_script_sl, pine_script_tp
            )
            
            # Calculate quantity based on position size and account balance
            account_balance = self.get_account_balance()
            trade_value = account_balance * position_size
            quantity = int(trade_value / entry_price)
            
            cursor.execute("""
                INSERT INTO institutional_trades (
                    symbol, signal, price, quantity, confidence, status,
                    entry_price, stop_loss, take_profit
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                alert_data['symbol'],
                alert_data['signal'],
                entry_price,
                quantity,
                alert_data['confidence'],
                status,
                entry_price,
                stop_loss,
                take_profit
            ))
            
            trade_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            logger.info(f"Trade logged: ID {trade_id}, {alert_data['symbol']} "
                       f"{alert_data['signal']} {quantity} @ {entry_price}")
            
            return trade_id
            
        except Exception as e:
            logger.error(f"Trade logging error: {str(e)}")
            return 0

    def process_signal(self, alert_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main signal processing function

        Args:
            alert_data: Dictionary containing signal data from TradingView

        Returns:
            Dictionary with processing result
        """
        try:
            logger.info(f"Processing signal for {alert_data.get('symbol')}")

            # Validate signal
            is_valid, message = self.validate_signal(alert_data)
            if not is_valid:
                logger.warning(f"Signal validation failed: {message}")
                return {
                    "status": "rejected",
                    "reason": message,
                    "timestamp": datetime.now().isoformat()
                }

            # Calculate position size (use Pine Script size if available)
            account_balance = self.get_account_balance()
            pine_script_position_size = alert_data.get('position_size')
            position_size = self.calculate_position_size(
                alert_data['confidence'],
                account_balance,
                float(alert_data['price']),
                pine_script_position_size
            )

            # Log trade
            trade_id = self.log_trade(alert_data, position_size, "pending")

            # Prepare trade execution data
            result = {
                "status": "accepted",
                "trade_id": trade_id,
                "symbol": alert_data['symbol'],
                "signal": alert_data['signal'],
                "position_size": position_size,
                "confidence": alert_data['confidence'],
                "entry_price": float(alert_data['price']),
                "timestamp": datetime.now().isoformat(),
                "message": "Signal processed successfully"
            }

            logger.info(f"Signal processed successfully: {result}")
            return result

        except Exception as e:
            logger.error(f"Signal processing error: {str(e)}")
            return {
                "status": "error",
                "reason": f"Processing error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

# Global strategy instance
institutional_strategy = InstitutionalStrategy()

def process_institutional_signal(alert_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Entry point for processing institutional signals

    Args:
        alert_data: Signal data from webhook

    Returns:
        Processing result
    """
    return institutional_strategy.process_signal(alert_data)
