//@version=6
strategy("Institutional Trading Strategy - Indian Markets", 
         shorttitle="ITS-India", 
         overlay=true, 
         initial_capital=100000, 
         default_qty_type=strategy.percent_of_equity, 
         default_qty_value=10,
         commission_type=strategy.commission.percent, 
         commission_value=0.1,
         slippage=2)

// ═══════════════════════════════════════════════════════════════════════════════════════
// STRATEGY DESCRIPTION
// ═══════════════════════════════════════════════════════════════════════════════════════
// This strategy is designed to predict institutional investor entry and exit points
// in Indian stock markets (NSE/BSE). It analyzes volume patterns, price action, and
// smart money flow to generate signals before major institutional moves.
//
// Key Features:
// 1. Volume Analysis - Detects unusual volume spikes and patterns
// 2. Institutional Footprint - Identifies accumulation/distribution phases
// 3. Breakout Prediction - Signals before major price movements
// 4. Indian Market Optimization - Tuned for NSE/BSE characteristics
// ═══════════════════════════════════════════════════════════════════════════════════════

// INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Pre-Institutional Detection Settings
pre_accumulation_length = input.int(30, "Pre-Accumulation Detection Length", minval=10, maxval=50, group="Pre-Institutional Detection")
stealth_volume_threshold = input.float(1.5, "Stealth Volume Threshold", minval=1.1, maxval=2.0, step=0.1, group="Pre-Institutional Detection")
price_compression_period = input.int(15, "Price Compression Period", minval=5, maxval=30, group="Pre-Institutional Detection")

// Volume Analysis Settings
volume_ma_length = input.int(20, "Volume MA Length", minval=5, maxval=100, group="Volume Analysis")
volume_spike_threshold = input.float(3.0, "Volume Spike Threshold", minval=1.5, maxval=5.0, step=0.1, group="Volume Analysis")
obv_length = input.int(14, "OBV Smoothing Length", minval=5, maxval=50, group="Volume Analysis")

// Market Regime Detection
atr_length = input.int(14, "ATR Length for Market Regime", minval=5, maxval=30, group="Market Regime")
trend_strength_length = input.int(20, "Trend Strength Length", minval=10, maxval=50, group="Market Regime")
volatility_threshold = input.float(1.2, "Volatility Threshold", minval=0.8, maxval=2.0, step=0.1, group="Market Regime")

// Price Action Settings
breakout_length = input.int(25, "Breakout Detection Length", minval=10, maxval=50, group="Price Action")
support_resistance_strength = input.int(3, "S/R Strength", minval=2, maxval=10, group="Price Action")

// Institutional Detection Settings
accumulation_period = input.int(60, "Accumulation Period", minval=20, maxval=100, group="Institutional Detection")
distribution_threshold = input.float(0.7, "Distribution Threshold", minval=0.5, maxval=1.0, step=0.1, group="Institutional Detection")

// Risk Management
adaptive_stops = input.bool(true, "Use Adaptive Stops", group="Risk Management")
base_stop_loss = input.float(1.2, "Base Stop Loss %", minval=0.5, maxval=5.0, step=0.1, group="Risk Management")
base_take_profit = input.float(4.8, "Base Take Profit %", minval=1.0, maxval=15.0, step=0.1, group="Risk Management")
risk_reward_ratio = input.float(3.0, "Risk:Reward Ratio", minval=1.0, maxval=10.0, step=0.1, group="Risk Management")

// Strategy Settings
enable_long = input.bool(true, "Enable Long Trades", group="Strategy Settings")
enable_short = input.bool(true, "Enable Short Trades", group="Strategy Settings")
session_filter = input.session("0915-1530", "Trading Session (IST)", group="Strategy Settings")
min_signal_spacing = input.int(15, "Minimum Bars Between Signals", minval=3, maxval=20, group="Strategy Settings")

// Stock Selection Filters (Institutional Grade Stocks Only) - RELAXED
min_volume_filter = input.int(500000, "Minimum Daily Volume", minval=100000, maxval=10000000, group="Stock Selection")
min_price_filter = input.float(50.0, "Minimum Stock Price", minval=10.0, maxval=1000.0, step=10.0, group="Stock Selection")
enable_stock_filters = input.bool(false, "Enable Stock Quality Filters", group="Stock Selection")

// Market Condition Filters - RELAXED
enable_market_filter = input.bool(false, "Enable Market Condition Filter", group="Market Filters")
market_strength_threshold = input.float(0.4, "Market Strength Threshold", minval=0.3, maxval=1.0, step=0.1, group="Market Filters")
sector_rotation_length = input.int(10, "Sector Rotation Detection Length", minval=5, maxval=20, group="Market Filters")

// Position Sizing Enhancement - ADVANCED
enable_kelly_sizing = input.bool(true, "Enable Kelly Criterion Sizing", group="Position Sizing")
max_position_size = input.float(15.0, "Maximum Position Size %", minval=5.0, maxval=25.0, step=1.0, group="Position Sizing")
confidence_multiplier = input.float(1.5, "High Confidence Signal Multiplier", minval=1.0, maxval=3.0, step=0.1, group="Position Sizing")
enable_volatility_sizing = input.bool(true, "Enable Volatility-Based Sizing", group="Position Sizing")
volatility_lookback = input.int(20, "Volatility Lookback Period", minval=10, maxval=50, group="Position Sizing")

// Multi-Timeframe Analysis
enable_mtf_analysis = input.bool(true, "Enable Multi-Timeframe Analysis", group="Multi-Timeframe")
higher_tf = input.timeframe("240", "Higher Timeframe for Confirmation", tooltip="Recommended: 60 (1H) for aggressive intraday, 240 (4H) for conservative intraday, 1D for swing trading", group="Multi-Timeframe")
mtf_trend_strength = input.float(0.7, "MTF Trend Strength Threshold", minval=0.5, maxval=1.0, step=0.1, group="Multi-Timeframe")

// Advanced Institutional Detection
enable_block_trades = input.bool(true, "Enable Block Trade Detection", group="Advanced Detection")
block_trade_multiplier = input.float(2.0, "Block Trade Volume Multiplier", minval=1.5, maxval=5.0, step=0.1, group="Advanced Detection")
enable_dark_pool = input.bool(true, "Enable Dark Pool Detection", group="Advanced Detection")
enable_order_blocks = input.bool(true, "Enable Order Block Detection", group="Advanced Detection")

// Dynamic Exit Strategy
enable_dynamic_exits = input.bool(true, "Enable Dynamic Exit Strategy", group="Exit Strategy")
trailing_stop_atr = input.float(2.0, "Trailing Stop ATR Multiplier", minval=1.0, maxval=5.0, step=0.1, group="Exit Strategy")
partial_profit_level1 = input.float(2.0, "First Profit Target (R Multiple)", minval=1.0, maxval=4.0, step=0.1, group="Exit Strategy")
partial_profit_level2 = input.float(3.0, "Second Profit Target (R Multiple)", minval=2.0, maxval=6.0, step=0.1, group="Exit Strategy")
partial_exit_percent = input.float(50.0, "Partial Exit Percentage", minval=25.0, maxval=75.0, step=5.0, group="Exit Strategy")

// ═══════════════════════════════════════════════════════════════════════════════════════
// STOCK QUALITY FILTERS (Institutional Grade Stocks Only)
// ═══════════════════════════════════════════════════════════════════════════════════════

// Daily Volume Check (Ensure sufficient liquidity)
daily_volume = volume * (timeframe.multiplier == 1 ? 1 : timeframe.multiplier)
volume_ma_50 = ta.sma(daily_volume, 50)
sufficient_volume = not enable_stock_filters or volume_ma_50 >= min_volume_filter

// Price Level Check (Avoid penny stocks)
sufficient_price = not enable_stock_filters or close >= min_price_filter

// Liquidity Quality Check
bid_ask_spread_proxy = (high - low) / close * 100
avg_spread = ta.sma(bid_ask_spread_proxy, 20)
good_liquidity = avg_spread < 2.0  // Less than 2% average spread

// Stock Quality Score
stock_quality_score = (sufficient_volume ? 1 : 0) + (sufficient_price ? 1 : 0) + (good_liquidity ? 1 : 0)
institutional_grade_stock = stock_quality_score >= 2

// ═══════════════════════════════════════════════════════════════════════════════════════
// MARKET CONDITION FILTERS (Enhanced Market Analysis)
// ═══════════════════════════════════════════════════════════════════════════════════════

// Market Breadth Proxy (Using volume and price action)
advancing_volume = volume * (close > close[1] ? 1 : 0)
declining_volume = volume * (close < close[1] ? 1 : 0)
volume_ratio = ta.sma(advancing_volume, sector_rotation_length) / ta.sma(declining_volume + advancing_volume, sector_rotation_length)

// Market Strength Indicator
price_momentum_5 = (close - close[5]) / close[5]
price_momentum_10 = (close - close[10]) / close[10]
momentum_consistency = price_momentum_5 * price_momentum_10 > 0 ? 1 : 0

// Sector Health Check (Proxy using relative strength)
relative_strength = close / ta.sma(close, 50)
sector_strength = ta.sma(relative_strength, sector_rotation_length)
healthy_sector = sector_strength > 1.0

// Overall Market Condition Score
market_condition_score = volume_ratio + momentum_consistency + (healthy_sector ? 1 : 0)
favorable_market = not enable_market_filter or market_condition_score >= market_strength_threshold * 3

// ═══════════════════════════════════════════════════════════════════════════════════════
// MARKET REGIME DETECTION (Works in all market conditions)
// ═══════════════════════════════════════════════════════════════════════════════════════

// Market Volatility Analysis
atr_current = ta.atr(atr_length)
atr_avg = ta.sma(atr_current, atr_length)
volatility_ratio = atr_current / atr_avg

// Market Regime Classification
trending_market = volatility_ratio > volatility_threshold
ranging_market = volatility_ratio < (2 - volatility_threshold)
volatile_market = volatility_ratio > (volatility_threshold * 1.5)

// Trend Strength Detection
price_ma_fast = ta.sma(close, 10)
price_ma_slow = ta.sma(close, trend_strength_length)
trend_strength = math.abs(price_ma_fast - price_ma_slow) / price_ma_slow * 100

strong_uptrend = close > price_ma_fast and price_ma_fast > price_ma_slow and trend_strength > 1.0
strong_downtrend = close < price_ma_fast and price_ma_fast < price_ma_slow and trend_strength > 1.0
sideways_trend = not strong_uptrend and not strong_downtrend

// ═══════════════════════════════════════════════════════════════════════════════════════
// PRE-INSTITUTIONAL DETECTION (Entry BEFORE institutions)
// ═══════════════════════════════════════════════════════════════════════════════════════

// Stealth Volume Analysis (Institutions building positions quietly)
volume_ma = ta.sma(volume, volume_ma_length)
stealth_volume = volume > (volume_ma * stealth_volume_threshold) and volume < (volume_ma * volume_spike_threshold)

// Volume moving averages for consistent calculations
volume_ma_5 = ta.sma(volume, 5)
volume_ma_15 = ta.sma(volume, 15)
volume_ma_30 = ta.sma(volume, 30)

// Price Compression Detection (Institutions accumulating in tight range)
price_range = ta.atr(price_compression_period)
avg_range = ta.sma(price_range, price_compression_period)
price_compression = price_range < (avg_range * 0.7)

// Smart Money Preparation Signals
volume_buildup = volume_ma_5 > volume_ma_15 and volume_ma_15 > volume_ma_30
price_stability = price_compression and stealth_volume

// Pre-Accumulation Signal (BEFORE major institutional entry)
pre_accumulation = price_stability and volume_buildup and not volatile_market

// ═══════════════════════════════════════════════════════════════════════════════════════
// VOLUME ANALYSIS FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Volume Rate of Change
volume_roc = (volume - volume[1]) / volume[1] * 100

// On Balance Volume with smoothing
obv_raw = ta.obv
obv_smooth = ta.sma(obv_raw, obv_length)
obv_signal = ta.sma(obv_smooth, 9)

// Volume Spike Detection (Institutional entry happening NOW)
volume_spike = volume > (volume_ma * volume_spike_threshold)
volume_above_avg = volume > volume_ma

// Institutional Volume Pattern Detection
price_change_pct = math.abs(ta.change(close) / close[1]) * 100
volume_price_divergence = volume_above_avg and price_change_pct < 1.0

// ═══════════════════════════════════════════════════════════════════════════════════════
// ADVANCED INSTITUTIONAL DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Multi-Timeframe Analysis
htf_close = request.security(syminfo.tickerid, higher_tf, close)
htf_volume = request.security(syminfo.tickerid, higher_tf, volume)
htf_trend = ta.sma(htf_close, 20)
htf_trend_direction = htf_close > htf_trend ? 1 : htf_close < htf_trend ? -1 : 0
htf_trend_strength_val = math.abs(htf_close - htf_trend) / htf_trend * 100

// Multi-timeframe confirmation
mtf_bullish_confirmation = enable_mtf_analysis ? htf_trend_direction == 1 and htf_trend_strength_val > mtf_trend_strength : true
mtf_bearish_confirmation = enable_mtf_analysis ? htf_trend_direction == -1 and htf_trend_strength_val > mtf_trend_strength : true

// Advanced Block Trade Detection
avg_volume_20 = ta.sma(volume, 20)
block_trade_threshold = avg_volume_20 * block_trade_multiplier
price_impact = math.abs(close - open) / ta.atr(14)
block_trade_detected = enable_block_trades and volume > block_trade_threshold and price_impact > 0.5

// Dark Pool Activity Detection (Hidden Institutional Orders)
price_efficiency = math.abs(close - open) / (high - low + 0.0001)
volume_efficiency = volume / ta.sma(volume, 10)
dark_pool_activity = enable_dark_pool and price_efficiency < 0.3 and volume_efficiency > 1.5 and volume_above_avg

// Order Block Detection (Institutional Supply/Demand Zones)
recent_high = ta.highest(high, 5)
recent_low = ta.lowest(low, 5)
bullish_order_block = enable_order_blocks and close > open and volume > avg_volume_20 * 1.3 and close > recent_high[1]
bearish_order_block = enable_order_blocks and close < open and volume > avg_volume_20 * 1.3 and close < recent_low[1]

// Enhanced Institutional Footprint Detection
buying_pressure = close > open ? volume * (close - open) / (high - low + 0.0001) : 0
selling_pressure = close < open ? volume * (open - close) / (high - low + 0.0001) : 0
net_institutional_flow = ta.sma(buying_pressure - selling_pressure, 10)

// Cumulative Volume Delta (CVD) Analysis
cvd = ta.cum(buying_pressure - selling_pressure)
cvd_ma = ta.sma(cvd, 20)
cvd_divergence = (close > close[5] and cvd < cvd[5]) or (close < close[5] and cvd > cvd[5])

// Volatility-Based Position Sizing
current_volatility = ta.atr(volatility_lookback) / close * 100
avg_volatility = ta.sma(current_volatility, volatility_lookback)
volatility_adjustment = enable_volatility_sizing ? math.max(0.5, math.min(1.5, avg_volatility / current_volatility)) : 1.0

// ═══════════════════════════════════════════════════════════════════════════════════════
// PRICE ACTION ANALYSIS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Support and Resistance Levels
highest_high = ta.highest(high, breakout_length)
lowest_low = ta.lowest(low, breakout_length)

// Breakout Detection
bullish_breakout = close > highest_high[1] and volume > volume_ma
bearish_breakout = close < lowest_low[1] and volume > volume_ma

// Price Momentum
price_momentum = (close - close[14]) / close[14] * 100
momentum_bullish = price_momentum > 0 and price_momentum > price_momentum[1]
momentum_bearish = price_momentum < 0 and price_momentum < price_momentum[1]

// ═══════════════════════════════════════════════════════════════════════════════════════
// INSTITUTIONAL FOOTPRINT DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Accumulation/Distribution Line (Manual calculation)
ad_raw = close == high and close == low or high == low ? 0 : ((2 * close - low - high) / (high - low)) * volume
ad_line = ta.cum(ad_raw)
ad_ma = ta.sma(ad_line, accumulation_period)
ad_slope = ta.change(ad_ma, 5)

// Smart Money Flow Index
typical_price = hlc3
money_flow = typical_price * volume
positive_flow = money_flow * (close > close[1] ? 1 : 0)
negative_flow = money_flow * (close < close[1] ? 1 : 0)

positive_flow_sum = ta.sma(positive_flow, 14)
negative_flow_sum = ta.sma(negative_flow, 14)
money_flow_ratio = positive_flow_sum / (negative_flow_sum == 0 ? 1 : negative_flow_sum)
money_flow_index = 100 - (100 / (1 + money_flow_ratio))

// Institutional Accumulation Signal
accumulation_signal = ad_slope > 0 and obv_smooth > obv_signal and money_flow_index > 50
distribution_signal = ad_slope < 0 and obv_smooth < obv_signal and money_flow_index < 50

// Volume Weighted Average Price (VWAP) Analysis
vwap = ta.vwap(hlc3)
price_above_vwap = close > vwap
vwap_slope = ta.change(vwap, 5) > 0

// ═══════════════════════════════════════════════════════════════════════════════════════
// ADAPTIVE SIGNAL GENERATION (Works in all market conditions)
// ═══════════════════════════════════════════════════════════════════════════════════════

// Session Filter
in_session = not na(time(timeframe.period, session_filter))

// Signal Spacing Control (Prevent overtrading)
bars_since_last_signal = ta.barssince(ta.change(strategy.position_size) != 0)
signal_spacing_ok = na(bars_since_last_signal) or bars_since_last_signal >= min_signal_spacing

// Calculate moving averages once for consistency
price_ma_20 = ta.sma(close, 20)

// ═══════════════════════════════════════════════════════════════════════════════════════
// PRE-INSTITUTIONAL ENTRY SIGNALS (Enter BEFORE institutions)
// ═══════════════════════════════════════════════════════════════════════════════════════

// Enhanced Early Long Signals (Before institutional breakout)
early_long_setup = pre_accumulation and close > vwap and vwap_slope
early_long_confirmation = obv_smooth > obv_signal and money_flow_index > 45
early_long_timing = close > ta.highest(close, 5)[1] and stealth_volume

// Advanced signal enhancements
advanced_long_signals = (block_trade_detected and net_institutional_flow > 0) or bullish_order_block or (dark_pool_activity and buying_pressure > selling_pressure)
mtf_long_filter = enable_mtf_analysis ? mtf_bullish_confirmation : true

// Market Condition Adaptations for Long (Enhanced)
long_trending_condition = trending_market and strong_uptrend and early_long_setup and mtf_long_filter
long_ranging_condition = ranging_market and early_long_confirmation and close > highest_high[5] and not cvd_divergence
long_volatile_condition = volatile_market and early_long_timing and volume_buildup and advanced_long_signals

// Enhanced trend filter for better signal quality
strong_trend_filter = (trend_strength > 2.0) or (ranging_market and volume_spike and volume > volume_ma * 2.5) or advanced_long_signals

// Combined Early Long Entry (Enhanced with advanced features)
early_long_entry = enable_long and in_session and signal_spacing_ok and strong_trend_filter and institutional_grade_stock and favorable_market and mtf_long_filter and (long_trending_condition or long_ranging_condition or long_volatile_condition)

// Enhanced Early Short Signals (Before institutional distribution)
early_short_setup = distribution_signal and close < vwap and not vwap_slope
early_short_confirmation = obv_smooth < obv_signal and money_flow_index < 55
early_short_timing = close < ta.lowest(close, 5)[1] and stealth_volume

// Advanced short signal enhancements
advanced_short_signals = (block_trade_detected and net_institutional_flow < 0) or bearish_order_block or (dark_pool_activity and selling_pressure > buying_pressure)
mtf_short_filter = enable_mtf_analysis ? mtf_bearish_confirmation : true

// Market Condition Adaptations for Short (Enhanced)
short_trending_condition = trending_market and strong_downtrend and early_short_setup and mtf_short_filter
short_ranging_condition = ranging_market and early_short_confirmation and close < lowest_low[5] and not cvd_divergence
short_volatile_condition = volatile_market and early_short_timing and volume_buildup and advanced_short_signals

// Combined Early Short Entry (Enhanced with advanced features)
early_short_entry = enable_short and in_session and signal_spacing_ok and strong_trend_filter and institutional_grade_stock and favorable_market and mtf_short_filter and (short_trending_condition or short_ranging_condition or short_volatile_condition)

// ═══════════════════════════════════════════════════════════════════════════════════════
// CONFIRMATION SIGNALS (Traditional institutional detection)
// ═══════════════════════════════════════════════════════════════════════════════════════

// Traditional Long Entry (When institutions are already entering)
traditional_long_volume = volume_spike and momentum_bullish
traditional_long_breakout = close > highest_high[1] and volume > volume_ma * 2.0
traditional_long_institutional = accumulation_signal and price_above_vwap and vwap_slope

traditional_long_entry = enable_long and in_session and signal_spacing_ok and institutional_grade_stock and favorable_market and (traditional_long_volume or traditional_long_breakout) and traditional_long_institutional

// Traditional Short Entry (When institutions are already selling)
traditional_short_volume = volume_spike and momentum_bearish
traditional_short_breakout = close < lowest_low[1] and volume > volume_ma * 2.0
traditional_short_institutional = distribution_signal and not price_above_vwap and not vwap_slope

traditional_short_entry = enable_short and in_session and signal_spacing_ok and institutional_grade_stock and favorable_market and (traditional_short_volume or traditional_short_breakout) and traditional_short_institutional

// Final Entry Signals (Prioritize early signals)
long_entry = early_long_entry or (traditional_long_entry and not early_long_entry[1] and not early_long_entry[2])
short_entry = early_short_entry or (traditional_short_entry and not early_short_entry[1] and not early_short_entry[2])

// ═══════════════════════════════════════════════════════════════════════════════════════
// ADAPTIVE RISK MANAGEMENT & DYNAMIC EXITS
// ═══════════════════════════════════════════════════════════════════════════════════════

// Adaptive Stop Loss based on market conditions
volatility_multiplier = adaptive_stops ? math.max(0.7, math.min(2.0, volatility_ratio)) : 1.0
market_stop_loss = base_stop_loss * volatility_multiplier
market_take_profit = base_take_profit * volatility_multiplier

// Dynamic Exit Strategy Variables
var float trailing_stop_long = na
var float trailing_stop_short = na
var float profit_target_1 = na
var float profit_target_2 = na
var bool partial_exit_1_triggered = false
var bool partial_exit_2_triggered = false

// ATR-based trailing stop calculation
atr_value = ta.atr(14)
trailing_atr_distance = atr_value * trailing_stop_atr

// Reset variables on new position
if strategy.position_size != 0 and strategy.position_size[1] == 0
    partial_exit_1_triggered := false
    partial_exit_2_triggered := false
    if strategy.position_size > 0  // Long position
        profit_target_1 := close * (1 + (market_take_profit / 100) * partial_profit_level1 / 4.0)
        profit_target_2 := close * (1 + (market_take_profit / 100) * partial_profit_level2 / 4.0)
        trailing_stop_long := close - trailing_atr_distance
    else  // Short position
        profit_target_1 := close * (1 - (market_take_profit / 100) * partial_profit_level1 / 4.0)
        profit_target_2 := close * (1 - (market_take_profit / 100) * partial_profit_level2 / 4.0)
        trailing_stop_short := close + trailing_atr_distance

// Update trailing stops
if strategy.position_size > 0 and enable_dynamic_exits
    new_trailing_stop = close - trailing_atr_distance
    trailing_stop_long := math.max(trailing_stop_long, new_trailing_stop)

if strategy.position_size < 0 and enable_dynamic_exits
    new_trailing_stop = close + trailing_atr_distance
    trailing_stop_short := math.min(trailing_stop_short, new_trailing_stop)

// ═══════════════════════════════════════════════════════════════════════════════════════
// KELLY CRITERION POSITION SIZING
// ═══════════════════════════════════════════════════════════════════════════════════════

// Enhanced Kelly Criterion Calculation with Dynamic Inputs
// Using current strategy performance metrics
win_rate = 0.26  // Current observed win rate (will be updated based on performance)
loss_rate = 1 - win_rate
odds_ratio = 4.0  // Our 4:1 risk-reward ratio

// Kelly Fraction with volatility adjustment
base_kelly_fraction = (odds_ratio * win_rate - loss_rate) / odds_ratio
volatility_adjusted_kelly = base_kelly_fraction * volatility_adjustment
kelly_percentage = math.max(0, math.min(volatility_adjusted_kelly * 100, max_position_size))

// Enhanced Signal Confidence Scoring
confidence_score_long = 0.0
confidence_score_short = 0.0

// Add confidence points for various factors (Enhanced)
if early_long_entry
    confidence_score_long += 2.5  // Early entry gets higher bonus
if accumulation_signal
    confidence_score_long += 1.5
if volume_spike
    confidence_score_long += 1.0
if trending_market
    confidence_score_long += 1.0
if institutional_grade_stock
    confidence_score_long += 1.0
if favorable_market
    confidence_score_long += 1.0

// New advanced factors for long confidence
if block_trade_detected
    confidence_score_long += 1.5  // Block trades are strong signals
if bullish_order_block
    confidence_score_long += 1.0
if mtf_bullish_confirmation
    confidence_score_long += 1.0  // Multi-timeframe confirmation
if dark_pool_activity and net_institutional_flow > 0
    confidence_score_long += 0.8
if not cvd_divergence  // No divergence is good
    confidence_score_long += 0.5

if early_short_entry
    confidence_score_short += 2.5
if distribution_signal
    confidence_score_short += 1.5
if volume_spike
    confidence_score_short += 1.0
if trending_market
    confidence_score_short += 1.0
if institutional_grade_stock
    confidence_score_short += 1.0
if favorable_market
    confidence_score_short += 1.0

// New advanced factors for short confidence
if block_trade_detected
    confidence_score_short += 1.5
if bearish_order_block
    confidence_score_short += 1.0
if mtf_bearish_confirmation
    confidence_score_short += 1.0
if dark_pool_activity and net_institutional_flow < 0
    confidence_score_short += 0.8
if not cvd_divergence
    confidence_score_short += 0.5

// Normalize confidence scores (0-1 scale) - RELAXED
max_confidence = 6.0  // Reduced from 7.5 to make scoring easier
normalized_confidence_long = math.min(confidence_score_long / max_confidence, 1.0)
normalized_confidence_short = math.min(confidence_score_short / max_confidence, 1.0)

// Dynamic Position Sizing
base_position_size = enable_kelly_sizing ? kelly_percentage : 10.0
dynamic_size_long = base_position_size * (1 + normalized_confidence_long * (confidence_multiplier - 1))
dynamic_size_short = base_position_size * (1 + normalized_confidence_short * (confidence_multiplier - 1))

// Final position sizes (capped at maximum)
final_long_size = math.min(dynamic_size_long, max_position_size)
final_short_size = math.min(dynamic_size_short, max_position_size)

// Position sizing based on signal strength (Legacy for compatibility)
signal_strength_long = (early_long_entry ? 1.5 : 1.0) * (trending_market ? 1.2 : ranging_market ? 0.8 : 1.0)
signal_strength_short = (early_short_entry ? 1.5 : 1.0) * (trending_market ? 1.2 : ranging_market ? 0.8 : 1.0)

// ═══════════════════════════════════════════════════════════════════════════════════════
// STRATEGY EXECUTION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Calculate Stop Loss and Take Profit Levels
long_stop = close * (1 - market_stop_loss / 100)
long_target = close * (1 + market_take_profit / 100)
short_stop = close * (1 + market_stop_loss / 100)
short_target = close * (1 - market_take_profit / 100)

// Execute Trades with dynamic position sizing and enhanced comments
if long_entry and strategy.position_size == 0
    entry_comment = early_long_entry ? "Early Long (Pre-Institutional)" : "Traditional Long"
    confidence_comment = " | Conf: " + str.tostring(math.round(normalized_confidence_long * 100)) + "%"
    size_comment = " | Size: " + str.tostring(math.round(final_long_size, 1)) + "%"
    mtf_comment = enable_mtf_analysis and mtf_bullish_confirmation ? " | MTF✓" : ""
    full_comment = entry_comment + confidence_comment + size_comment + mtf_comment

    // Calculate position size as percentage of equity
    position_size_percent = final_long_size
    strategy.entry("Long", strategy.long, qty=strategy.equity * position_size_percent / 100 / close, comment=full_comment)

    // Dynamic exit strategy
    if enable_dynamic_exits
        strategy.exit("Long Exit", "Long", stop=trailing_stop_long, comment="Long Exit (Trailing)")
    else
        strategy.exit("Long Exit", "Long", stop=long_stop, limit=long_target, comment="Long Exit")

if short_entry and strategy.position_size == 0
    entry_comment = early_short_entry ? "Early Short (Pre-Institutional)" : "Traditional Short"
    confidence_comment = " | Conf: " + str.tostring(math.round(normalized_confidence_short * 100)) + "%"
    size_comment = " | Size: " + str.tostring(math.round(final_short_size, 1)) + "%"
    mtf_comment = enable_mtf_analysis and mtf_bearish_confirmation ? " | MTF✓" : ""
    full_comment = entry_comment + confidence_comment + size_comment + mtf_comment

    // Calculate position size as percentage of equity
    position_size_percent = final_short_size
    strategy.entry("Short", strategy.short, qty=strategy.equity * position_size_percent / 100 / close, comment=full_comment)

    // Dynamic exit strategy
    if enable_dynamic_exits
        strategy.exit("Short Exit", "Short", stop=trailing_stop_short, comment="Short Exit (Trailing)")
    else
        strategy.exit("Short Exit", "Short", stop=short_stop, limit=short_target, comment="Short Exit")

// Partial Profit Taking (Dynamic Exits)
if enable_dynamic_exits and strategy.position_size > 0
    if close >= profit_target_1 and not partial_exit_1_triggered
        strategy.close("Long", qty_percent=partial_exit_percent, comment="Partial Long Exit 1")
        partial_exit_1_triggered := true
    if close >= profit_target_2 and not partial_exit_2_triggered
        strategy.close("Long", qty_percent=25, comment="Partial Long Exit 2")
        partial_exit_2_triggered := true

if enable_dynamic_exits and strategy.position_size < 0
    if close <= profit_target_1 and not partial_exit_1_triggered
        strategy.close("Short", qty_percent=partial_exit_percent, comment="Partial Short Exit 1")
        partial_exit_1_triggered := true
    if close <= profit_target_2 and not partial_exit_2_triggered
        strategy.close("Short", qty_percent=25, comment="Partial Short Exit 2")
        partial_exit_2_triggered := true

// Update trailing stops for existing positions
if enable_dynamic_exits and strategy.position_size > 0
    strategy.exit("Long Trailing", "Long", stop=trailing_stop_long, comment="Long Trailing Stop")

if enable_dynamic_exits and strategy.position_size < 0
    strategy.exit("Short Trailing", "Short", stop=trailing_stop_short, comment="Short Trailing Stop")

// ═══════════════════════════════════════════════════════════════════════════════════════
// VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════════════════

// Plot VWAP
plot(vwap, "VWAP", color=color.yellow, linewidth=2)

// Plot Support and Resistance
plot(highest_high, "Resistance", color=color.red, linewidth=1, style=plot.style_stepline)
plot(lowest_low, "Support", color=color.green, linewidth=1, style=plot.style_stepline)

// Background Colors for Market Conditions and Signals
bgcolor(trending_market ? color.new(color.blue, 97) : na, title="Trending Market")
bgcolor(ranging_market ? color.new(color.yellow, 97) : na, title="Ranging Market")
bgcolor(volatile_market ? color.new(color.orange, 97) : na, title="Volatile Market")

// Background Colors for Stock Quality and Market Filters
bgcolor(not institutional_grade_stock ? color.new(color.gray, 90) : na, title="Low Quality Stock")
bgcolor(not favorable_market ? color.new(color.purple, 95) : na, title="Unfavorable Market")

// Background Colors for Institutional Phases
bgcolor(pre_accumulation ? color.new(color.aqua, 95) : na, title="Pre-Accumulation (Early Warning)")
bgcolor(accumulation_signal ? color.new(color.blue, 95) : na, title="Accumulation")
bgcolor(distribution_signal ? color.new(color.orange, 95) : na, title="Distribution")

// Background Colors for Entry Signals
bgcolor(early_long_entry ? color.new(color.lime, 85) : na, title="Early Long Signal")
bgcolor(early_short_entry ? color.new(color.red, 85) : na, title="Early Short Signal")
bgcolor(traditional_long_entry ? color.new(color.green, 90) : na, title="Traditional Long Signal")
bgcolor(traditional_short_entry ? color.new(color.maroon, 90) : na, title="Traditional Short Signal")

// Plot Shapes for Entry Signals (Different shapes for early vs traditional)
plotshape(early_long_entry, "Early Long", shape.diamond, location.belowbar, color.lime, size=size.small, text="EARLY")
plotshape(early_short_entry, "Early Short", shape.diamond, location.abovebar, color.red, size=size.small, text="EARLY")
plotshape(traditional_long_entry, "Traditional Long", shape.triangleup, location.belowbar, color.green, size=size.normal)
plotshape(traditional_short_entry, "Traditional Short", shape.triangledown, location.abovebar, color.red, size=size.normal)

// Volume and Activity Markers
plotchar(volume_spike, "Volume Spike", "V", location.top, color.purple, size=size.tiny)
plotchar(stealth_volume, "Stealth Volume", "s", location.top, color.blue, size=size.tiny)
plotchar(pre_accumulation, "Pre-Accumulation", "P", location.bottom, color.aqua, size=size.tiny)

// Advanced Detection Markers
plotchar(block_trade_detected, "Block Trade", "B", location.top, color.orange, size=size.small)
plotchar(dark_pool_activity, "Dark Pool", "D", location.top, color.navy, size=size.tiny)
plotchar(bullish_order_block, "Bull OB", "▲", location.bottom, color.lime, size=size.tiny)
plotchar(bearish_order_block, "Bear OB", "▼", location.top, color.red, size=size.tiny)
plotchar(cvd_divergence, "CVD Div", "⚠", location.top, color.yellow, size=size.tiny)

// Multi-timeframe confirmation
bgcolor(enable_mtf_analysis and mtf_bullish_confirmation ? color.new(color.green, 98) : na, title="MTF Bullish")
bgcolor(enable_mtf_analysis and mtf_bearish_confirmation ? color.new(color.red, 98) : na, title="MTF Bearish")

// Dynamic exit levels
plot(enable_dynamic_exits and strategy.position_size > 0 ? trailing_stop_long : na, "Long Trailing Stop", color.red, linewidth=1, style=plot.style_stepline)
plot(enable_dynamic_exits and strategy.position_size < 0 ? trailing_stop_short : na, "Short Trailing Stop", color.red, linewidth=1, style=plot.style_stepline)
plot(enable_dynamic_exits and strategy.position_size > 0 ? profit_target_1 : na, "Profit Target 1", color.green, linewidth=1, style=plot.style_stepline)
plot(enable_dynamic_exits and strategy.position_size > 0 ? profit_target_2 : na, "Profit Target 2", color.lime, linewidth=1, style=plot.style_stepline)
plot(enable_dynamic_exits and strategy.position_size < 0 ? profit_target_1 : na, "Short Target 1", color.green, linewidth=1, style=plot.style_stepline)
plot(enable_dynamic_exits and strategy.position_size < 0 ? profit_target_2 : na, "Short Target 2", color.lime, linewidth=1, style=plot.style_stepline)

// ═══════════════════════════════════════════════════════════════════════════════════════
// ALERTS (Using strategy alerts instead of alertcondition for strategies)
// ═══════════════════════════════════════════════════════════════════════════════════════

// Strategy alerts are automatically generated with strategy.entry() and strategy.exit() calls
// Additional custom alerts can be created using alert() function if needed

// Enhanced alert system for pre-institutional detection
if pre_accumulation and not pre_accumulation[1]
    alert("🚨 PRE-INSTITUTIONAL ACCUMULATION DETECTED - Institutions about to enter!", alert.freq_once_per_bar)

if early_long_entry and not early_long_entry[1]
    alert("🟢 EARLY LONG SIGNAL - Enter before institutions!", alert.freq_once_per_bar)

if early_short_entry and not early_short_entry[1]
    alert("🔴 EARLY SHORT SIGNAL - Enter before institutions!", alert.freq_once_per_bar)

if traditional_long_entry and not traditional_long_entry[1]
    alert("📈 INSTITUTIONAL LONG CONFIRMED - Major buying detected", alert.freq_once_per_bar)

if traditional_short_entry and not traditional_short_entry[1]
    alert("📉 INSTITUTIONAL SHORT CONFIRMED - Major selling detected", alert.freq_once_per_bar)

if volume_spike and not volume_spike[1]
    alert("💥 VOLUME SPIKE - Institutional activity happening NOW", alert.freq_once_per_bar)

// Enhanced alerts for new features
if not institutional_grade_stock and institutional_grade_stock[1]
    alert("⚠️ STOCK QUALITY WARNING - Stock no longer meets institutional criteria", alert.freq_once_per_bar)

if not favorable_market and favorable_market[1]
    alert("🌧️ MARKET CONDITIONS DETERIORATING - Consider reducing exposure", alert.freq_once_per_bar)

if enable_kelly_sizing and long_entry and normalized_confidence_long > 0.8
    alert("🎯 HIGH CONFIDENCE LONG - Kelly sizing suggests larger position", alert.freq_once_per_bar)

if enable_kelly_sizing and short_entry and normalized_confidence_short > 0.8
    alert("🎯 HIGH CONFIDENCE SHORT - Kelly sizing suggests larger position", alert.freq_once_per_bar)

// Advanced detection alerts
if block_trade_detected and not block_trade_detected[1]
    alert("🔥 BLOCK TRADE DETECTED - Large institutional order executed!", alert.freq_once_per_bar)

if dark_pool_activity and not dark_pool_activity[1]
    alert("🕳️ DARK POOL ACTIVITY - Hidden institutional orders detected", alert.freq_once_per_bar)

if bullish_order_block and not bullish_order_block[1]
    alert("📈 BULLISH ORDER BLOCK - Institutional demand zone created", alert.freq_once_per_bar)

if bearish_order_block and not bearish_order_block[1]
    alert("📉 BEARISH ORDER BLOCK - Institutional supply zone created", alert.freq_once_per_bar)

if cvd_divergence and not cvd_divergence[1]
    alert("⚠️ CVD DIVERGENCE - Price and volume flow diverging", alert.freq_once_per_bar)

if enable_mtf_analysis and mtf_bullish_confirmation and not mtf_bullish_confirmation[1]
    alert("✅ MULTI-TIMEFRAME BULLISH - Higher timeframe confirms uptrend", alert.freq_once_per_bar)

if enable_mtf_analysis and mtf_bearish_confirmation and not mtf_bearish_confirmation[1]
    alert("❌ MULTI-TIMEFRAME BEARISH - Higher timeframe confirms downtrend", alert.freq_once_per_bar)

// Dynamic exit alerts
if enable_dynamic_exits and strategy.position_size > 0 and partial_exit_1_triggered and not partial_exit_1_triggered[1]
    alert("💰 PARTIAL PROFIT TAKEN - First profit target reached", alert.freq_once_per_bar)

if enable_dynamic_exits and strategy.position_size < 0 and partial_exit_1_triggered and not partial_exit_1_triggered[1]
    alert("💰 PARTIAL PROFIT TAKEN - First short profit target reached", alert.freq_once_per_bar)
